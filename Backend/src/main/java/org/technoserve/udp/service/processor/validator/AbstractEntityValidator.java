package org.technoserve.udp.service.processor.validator;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Abstract base class for entity validators
 * @param <T> The entity type
 */
public abstract class AbstractEntityValidator<T> implements EntityValidator<T> {
    
    protected static final Logger logger = LoggerFactory.getLogger(AbstractEntityValidator.class);
    
    @Override
    public boolean validateRequiredFieldMappings(Map<String, String> mappings, Map<String, String> validationErrors) {
        Set<String> requiredFields = getRequiredFields();
        Set<String> mappedFields = new HashSet<>(mappings.keySet());
        
        // Find required fields that are not mapped
        Set<String> unmappedRequiredFields = requiredFields.stream()
            .filter(field -> !mappedFields.contains(field))
            .collect(Collectors.toSet());
        
        if (!unmappedRequiredFields.isEmpty()) {
            String errorMessage = "Required fields not mapped: " + String.join(", ", unmappedRequiredFields);
            validationErrors.put("MAPPING_ERROR", errorMessage);
            logger.warn(errorMessage);
            return false;
        }
        
        return true;
    }
    
    @Override
    public void performCustomValidations(T entity, Map<String, String> validationErrors) {
        // Default implementation does nothing
        // Override this method to add custom validations
    }
}
