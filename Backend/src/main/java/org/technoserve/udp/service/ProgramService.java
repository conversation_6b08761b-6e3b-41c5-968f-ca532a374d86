package org.technoserve.udp.service;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.stereotype.Service;
import org.technoserve.udp.dto.ApiResponse;
import org.technoserve.udp.dto.PartnerResponse;
import org.technoserve.udp.dto.ProgramRequest;
import org.technoserve.udp.dto.ProgramResponse;
import org.technoserve.udp.entity.common.Status;
import org.technoserve.udp.entity.program.*;
import org.technoserve.udp.entity.valuechain.ValueChain;
import org.technoserve.udp.exception.BadRequestException;
import org.technoserve.udp.exception.ConflictException;
import org.technoserve.udp.exception.ResourceNotFoundException;
import org.technoserve.udp.repository.FundCodeRepository;
import org.technoserve.udp.repository.PartnerRepository;
import org.technoserve.udp.repository.ProgramRepository;
import org.technoserve.udp.repository.ValueChainRepository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class ProgramService {

  private final ProgramRepository programRepository;
  private final PartnerRepository partnerRepository;
  private final ValueChainRepository valueChainRepository;
  private final FundCodeRepository fundCodeRepository;
  private final ModelMapper modelMapper;

  @Transactional
  public ApiResponse createProgram(ProgramRequest programDTO) {
    if (programRepository.existsByNameAndValueChain_ValueChainIdAndStatusNot(programDTO.getName(), programDTO.getValueChainId(), Status.DELETED)) {
      throw new ConflictException("A program with the same name already exists under this ValueChain");
    }
    Program program = modelMapper.map(programDTO, Program.class);
    if (programDTO.getValueChainId() != null) {
      ValueChain valueChain = valueChainRepository.findById(programDTO.getValueChainId()).orElseThrow(() -> new ResourceNotFoundException("ValueChain not found"));
      program.setValueChain(valueChain);
    }
    program.setProgramId(null);
    program.setStatus(Status.DRAFT);  // Default status
    Program savedProgram = programRepository.save(program);
    return new ApiResponse("Program created as Draft", savedProgram.getProgramId());
  }


  public ApiResponse updateProgram(Long id, ProgramRequest programDTO) {
    Program program = programRepository.findById(id).orElseThrow(() -> new ResourceNotFoundException("Program not found"));

    if (programRepository.existsByNameAndValueChain_ValueChainIdAndStatusNotAndProgramIdNot(programDTO.getName(), programDTO.getValueChainId(), Status.DELETED, id)) {
      throw new ConflictException("A program with the same name already exists under this ValueChain");
    }

    program.setName(programDTO.getName());
    program.setLogoURL(programDTO.getLogoURL());
    program.setStatus(Status.DRAFT);

    if (programDTO.getValueChainId() != null) {
      ValueChain valueChain = valueChainRepository.findById(programDTO.getValueChainId()).orElseThrow(() -> new ResourceNotFoundException("ValueChain not found"));
      program.setValueChain(valueChain);
    }

    Program updatedProgram = programRepository.save(program);
    return new ApiResponse("Program updated successfully", updatedProgram.getProgramId());
  }


  public ApiResponse softDeleteProgram(Long id) {
    Program program = programRepository.findById(id).orElseThrow(() -> new ResourceNotFoundException("Program not found"));

    program.setStatus(Status.DELETED);  // Soft delete
    programRepository.save(program);

    return new ApiResponse("Program deleted successfully", id);
  }


  public List<ProgramResponse> listActiveProgramsByValueChain(Long valueChainId) {
    List<Program> programs = programRepository.findByValueChain_ValueChainIdAndStatusNot(valueChainId, Status.DELETED);
    return programs.stream().map(program -> {
      ProgramResponse response = modelMapper.map(program, ProgramResponse.class);
      // Get fund codes for this program
      List<String> fundCodes = fundCodeRepository.findByProgramProgramIdAndStatus(program.getProgramId(), Status.CREATED).stream().map(FundCodeEntity::getFundCode).toList();
      response.setFundCodes(fundCodes);

      // Set program phase if present
      Optional.ofNullable(program.getProgramFactSheet())
          .map(ProgramFactSheet::getProgramPhase)
          .ifPresent(response::setProgramPhase);

      return response;
    }).sorted(Comparator.comparing(response -> getProgramPhaseOrder(response.getProgramPhase())))
        .toList();
  }

  public ApiResponse submitProgram(Long programId) {

    Program program = programRepository.findById(programId).orElseThrow(() -> new ResourceNotFoundException("Program not found"));
    program.setStatus(Status.SUBMITTED);
    program.setReviewComment(null);
    program.setApproveComment(null);
    programRepository.save(program);
    return new ApiResponse("Program is submitted for review", programId);

  }

  public ApiResponse reviewProgram(Long programId, String comment, OidcUser oidcUser) {

    Program program = programRepository.findById(programId).orElseThrow(() -> new ResourceNotFoundException("Program not found"));

    if (!program.getStatus().equals(Status.SUBMITTED)) {
      throw new BadRequestException("Can only review the submitted program");
    }

    program.setStatus(Status.CHECKED);
    program.setReviewComment(comment);
    program.setCheckedAt(LocalDateTime.now());
    program.setCheckedBy(oidcUser.getEmail());
    programRepository.save(program);
    return new ApiResponse("Program is reviewed by reviewer", programId);

  }

  public ApiResponse approveProgram(Long programId, String comment, OidcUser oidcUser) {

    Program program = programRepository.findById(programId).orElseThrow(() -> new ResourceNotFoundException("Program not found"));

    if (!program.getStatus().equals(Status.CHECKED)) {
      throw new BadRequestException("Can only approve the reviewed program");
    }

    program.setStatus(Status.APPROVED);
    program.setApproveComment(comment);
    program.setApprovedAt(LocalDateTime.now());
    program.setApprovedBy(oidcUser.getEmail());


    if (program.getProgramFactSheet() != null &&
        !program.getProgramFactSheet().getStartYear().isAfter(LocalDate.now()) &&
        !program.getProgramFactSheet().getEndYear().isBefore(LocalDate.now())) {

      program.getProgramFactSheet().setProgramPhase(ProgramPhase.ACTIVE);
    }

    programRepository.save(program);
    return new ApiResponse("Program is approved by reviewer", programId);

  }

  public ApiResponse rejectProgram(Long programId, String comment, OidcUser oidcUser) {

    Program program = programRepository.findById(programId).orElseThrow(() -> new ResourceNotFoundException("Program not found"));

    if (!program.getStatus().equals(Status.SUBMITTED) && !program.getStatus().equals(Status.CHECKED)) {
      throw new BadRequestException("Can only reject the submitted/reviewed program");
    }

    if(program.getStatus().equals(Status.CHECKED)) program.setApproveComment(comment);
    if(program.getStatus().equals(Status.SUBMITTED))  program.setReviewComment(comment);

    program.setStatus(Status.REJECTED);
    programRepository.save(program);
    return new ApiResponse("Program is rejected", programId);

  }

  /**
   * Get all partners by program ID
   *
   * @param programId The program ID
   * @return List of partner responses
   */
  public List<PartnerResponse> getPartnersByProgramId(Long programId) {
    // Verify that the program exists
    programRepository.findById(programId).orElseThrow(() -> new ResourceNotFoundException("Program not found with ID: " + programId));

    // Get all partners for the program
    List<Partner> partners = partnerRepository.findAllActivePartnersByProgramId(programId);

    // Convert to response DTOs
    return partners.stream().map(partner -> modelMapper.map(partner, PartnerResponse.class)).toList();
  }

  public List<ProgramResponse> listOfApprovedProgramsInValueChains(Long valueChainId) {
    List<Program> programs = programRepository.findByValueChain_ValueChainIdAndStatusNot(valueChainId, Status.DELETED);
    return programs.stream().filter(program -> program.getStatus().equals(Status.APPROVED)).map(program -> {
      ProgramResponse response = modelMapper.map(program, ProgramResponse.class);
      // Get fund codes for this program
      List<String> fundCodes = fundCodeRepository.findByProgramProgramIdAndStatus(program.getProgramId(), Status.CREATED).stream().map(FundCodeEntity::getFundCode).toList();
      response.setFundCodes(fundCodes);

      // Set program phase if present
      Optional.ofNullable(program.getProgramFactSheet())
          .map(ProgramFactSheet::getProgramPhase)
          .ifPresent(response::setProgramPhase);

      return response;
    }).sorted(Comparator.comparing(response -> getProgramPhaseOrder(response.getProgramPhase()))).toList();
  }

  private int getProgramPhaseOrder(ProgramPhase phase) {
    if (phase == null) return Integer.MAX_VALUE;
    return switch (phase) {
      case ACTIVE -> 1;
      case INACTIVE -> 2;
      default -> Integer.MAX_VALUE;
    };
  }

  /**
   * Update program phase to ACTIVE or INACTIVE
   *
   * @param programId The program ID
   * @param programPhase The new program phase
   * @return API response with success message
   */
  @Transactional
  public ApiResponse updateProgramPhase(Long programId, ProgramPhase programPhase) {
    // Verify that the program exists
    Program program = programRepository.findById(programId)
        .orElseThrow(() -> new ResourceNotFoundException("Program not found with ID: " + programId));

    // Check if program has a fact sheet
    if (program.getProgramFactSheet() == null) {
      throw new BadRequestException("Program does not have a fact sheet");
    }

    if(programPhase == ProgramPhase.ACTIVE){
        if(!program.getProgramFactSheet().getStartYear().isAfter(LocalDate.now()) &&
        !program.getProgramFactSheet().getEndYear().isBefore(LocalDate.now())) {

          program.getProgramFactSheet().setProgramPhase(ProgramPhase.ACTIVE);

        }
        else{
          throw new BadRequestException("Program cannot be set to Active. Start and end dates are not valid");
        }
    }


    // Update the program phase
    program.getProgramFactSheet().setProgramPhase(programPhase);
    programRepository.save(program);

    return new ApiResponse("Program phase updated to " + programPhase.getDisplayValue(), programId);
  }
}
