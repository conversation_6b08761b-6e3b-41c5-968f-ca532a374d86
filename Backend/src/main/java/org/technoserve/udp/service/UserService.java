package org.technoserve.udp.service;

import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.technoserve.udp.dto.UpdateUserRequest;
import org.technoserve.udp.dto.UserCreateRequest;
import org.technoserve.udp.dto.UserDto;
import org.technoserve.udp.entity.auth.Role;
import org.technoserve.udp.entity.auth.UserEntity;
import org.technoserve.udp.entity.auth.UserStatus;
import org.technoserve.udp.exception.BadRequestException;
import org.technoserve.udp.exception.ResourceNotFoundException;
import org.technoserve.udp.exception.RoleNotFoundException;
import org.technoserve.udp.exception.UserAlreadyExistsException;
import org.technoserve.udp.repository.RoleRepository;
import org.technoserve.udp.repository.UserRepository;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j // Enables logging
@Service
public class UserService {

  @Value("${user.email-domains-allowed}")
  private String allowedEmailDomains;

  private final UserRepository userRepository;
  private final RoleRepository roleRepository;
  private final ModelMapper modelMapper;

  public UserService(UserRepository userRepository, RoleRepository roleRepository, ModelMapper modelMapper) {
    this.userRepository = userRepository;
    this.roleRepository = roleRepository;
    this.modelMapper = modelMapper;
  }

  /**
   * Retrieves all users except those assigned the ADMIN role.
   *
   * @return List of UserDto objects representing non-admin users.
   */
  @Transactional(readOnly = true)
  public Page<UserDto> getAllUsers(String email, Pageable pageable) {
    log.info("Fetching users with email filter: {} and pagination: page {} size {}", email, pageable.getPageNumber(), pageable.getPageSize());

    Page<UserEntity> userPage = userRepository.findByEmailContainingIgnoreCaseAndStatusAndRoles_NameNot(email,UserStatus.ACTIVE,"ADMIN", pageable);

    // Convert to DTO
    return userPage.map(user -> {
      UserDto userDto = modelMapper.map(user, UserDto.class);
      Set<String> roleNames = user.getRoles().stream()
              .map(Role::getName)
              .collect(Collectors.toSet());
      userDto.setRoles(roleNames);
      return userDto;
    });
  }

  /**
   * Retrieves a user by email.
   *
   * @param email Email of the user to be retrieved.
   * @return UserEntity if found; otherwise, throws ResourceNotFoundException.
   */
  public UserEntity getUserByEmail(String email) {
    log.info("Fetching user by email: {}", email);
    return userRepository.findById(email)
            .orElseThrow(() -> {
              log.error("User with email {} not found", email);
              return new ResourceNotFoundException("User with email " + email + " not found");
            });
  }

  /**
   * Creates a new user with the specified roles.
   *
   * @param request UserCreateRequest containing user details.
   * @return Success message upon user creation.
   */
  @Transactional
  public String createUser(UserCreateRequest request) {
    log.info("Creating user with email: {}", request.getEmail());

    boolean allowed = Arrays.stream(allowedEmailDomains.split(","))
        .map(String::trim)
        .map(domain -> "@" + domain)
        .anyMatch(request.getEmail()::endsWith);

    if (!allowed) {
      throw new BadRequestException("User with email " + request.getEmail() + " not allowed");
    }

    // Check if user already exists
    UserEntity user = userRepository.findById(request.getEmail()).orElse(null);
// Fetch and validate roles
    Set<Role> validatedRoles = validateAndFetchRoles(request.getRoles());
    if (user != null) {
      if (user.getStatus() == UserStatus.DELETED) {
        user.setStatus(UserStatus.ACTIVE);
        user.setName(request.getName());
        user.setRoles(validatedRoles);
        userRepository.save(user);
        log.info("User {} reactivated successfully", request.getEmail());
        return "User created successfully";
      }
      log.error("User with email {} already exists", request.getEmail());
      throw new UserAlreadyExistsException("User with email " + request.getEmail() + " already exists");
    }

    // Create and save new user
    UserEntity newUser = UserEntity.builder()
            .email(request.getEmail())
            .name(request.getName())
            .status(UserStatus.ACTIVE)
            .roles(validatedRoles)
            .build();

    userRepository.save(newUser);
    log.info("User {} created successfully", request.getEmail());
    return "User created successfully";
  }

  /**
   * Validates and fetches roles from role repository.
   *
   * @param roles List of role names.
   * @return Set of valid Role entities.
   */
  private Set<Role> validateAndFetchRoles(List<String> roles) {
    return roles.stream()
            .map(role -> roleRepository.findByName(role)
                    .orElseThrow(() -> {
                      log.error("Role not found: {}", role);
                      return new RoleNotFoundException("Role not found: " + role);
                    }))
            .collect(Collectors.toSet());
  }

  /**
   * Updates an existing user’s details, including name and roles.
   *
   * @param email User's email used for identification.
   * @param updateUserRequest Object containing updated user details.
   * @return Success message upon update.
   */
  @Transactional
  public String updateUser(String email, UpdateUserRequest updateUserRequest) {
    log.info("Updating user with email: {}", email);

    return userRepository.findById(email).map(user -> {
      user.setName(updateUserRequest.getName());

      if (updateUserRequest.getRoles() != null && !updateUserRequest.getRoles().isEmpty()) {
        Set<Role> updatedRoles = updateUserRequest.getRoles().stream()
                .map(roleName -> roleRepository.findByName(roleName)
                        .orElseThrow(() -> {
                          log.error("Role not found: {}", roleName);
                          return new ResourceNotFoundException("Role not found: " + roleName);
                        }))
                .collect(Collectors.toSet());

        user.setRoles(updatedRoles);
      }

      userRepository.save(user);
      log.info("User {} updated successfully", email);
      return "User updated successfully";
    }).orElseThrow(() -> {
      log.error("User not found with email: {}", email);
      return new ResourceNotFoundException("User not found with email: " + email);
    });
  }



  /**
   * Changes the status of an existing user.
   *
   * @param email Email of the user whose status needs updating.
   * @param userStatus New status to be assigned.
   * @return Success message upon status update.
   */
  @Transactional
  public String changeStatus(String email, UserStatus userStatus) {
    log.info("Changing status of user {} to {}", email, userStatus);

    return userRepository.findById(email).map(user -> {
      if (user.getStatus() == userStatus) {
        log.warn("User {} already has status {}", email, userStatus);
        throw new IllegalStateException("User already has the status: " + userStatus);
      }

      user.setStatus(userStatus);
      userRepository.save(user);
      log.info("User {} status changed to {}", email, userStatus);
      return "User status updated successfully";
    }).orElseThrow(() -> {
      log.error("User not found with email: {}", email);
      return new ResourceNotFoundException("User not found with email: " + email);
    });
  }

}
