package org.technoserve.udp.service.processor;

import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.technoserve.udp.entity.dataflow.Centre;
import org.technoserve.udp.entity.dataflow.Designation;
import org.technoserve.udp.entity.dataflow.ExcelFileMetaData;
import org.technoserve.udp.entity.dataflow.Staff;
import org.technoserve.udp.entity.program.Partner;
import org.technoserve.udp.entity.program.Program;
import org.technoserve.udp.repository.CentreRepository;
import org.technoserve.udp.repository.DesignationRepository;
import org.technoserve.udp.repository.StaffRepository;
import org.technoserve.udp.service.processor.validator.StaffValidator;
import org.technoserve.udp.util.UdpCommonUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Processor for Staff data
 */
public class StaffDataProcessor extends AbstractDataProcessor<Staff> {

  private final StaffRepository staffRepository;
  private final CentreRepository centreRepository;
  private final DesignationRepository designationRepository;

  public StaffDataProcessor(StaffRepository staffRepository, CentreRepository centreRepository, DesignationRepository designationRepository, FormulaEvaluator evaluator) {
    super(new StaffValidator(centreRepository, designationRepository), evaluator);
    this.staffRepository = staffRepository;
    this.centreRepository = centreRepository;
    this.designationRepository = designationRepository;
  }

  @Override
  protected Staff createEntity(Program program, Partner partner, ExcelFileMetaData excelFileMetaData) {
    return Staff.builder()
        .programId(program.getProgramId())
        .partnerId(partner.getPartnerId())
        .excelFileMetaData(excelFileMetaData)
        .build();
  }

  @Override
  protected void processIdField(Row row, Map<Integer, String> columnIndices, Staff entity, Map<String, String> validationErrors, Map<String, String> mappings) {
    // Check if we have a Staff_ID
    String staffId = null;
    String staffIdExcelColumn = mappings.get("staffId");
    Map<String, String> columnValues = getColumnValueMappings(row, columnIndices);

    staffId = columnValues.get(staffIdExcelColumn);
    if (staffId == null || staffId.isEmpty()) {
      validationErrors.put("Staff_ID", "Staff ID is required");
    } else {
      entity.setStaffId(staffId);
    }
  }


  protected int processBatch(Sheet sheet, int startRow, int endRow, Map<Integer, String> columnIndices, Map<String, String> mappings,
                             Program program, Partner partner, ExcelFileMetaData excelFileMetaData) {
    List<Staff> staffToSave = new ArrayList<>();
    int batchRecordsProcessed = 0;

    // Process each row in the batch
    for (int i = startRow; i <= endRow; i++) {
      Row row = sheet.getRow(i);
      if (row == null) continue;

      // Process the row and get the staff entity
      Staff staff = processRow(row, columnIndices, mappings, program, partner, excelFileMetaData);

      // Add to batch if row has data
      if (staff != null) {
        // Apply any transformations to the data
        staff.setGender(UdpCommonUtil.toBeginningUpperCase(staff.getGender()));
        staff.setState(UdpCommonUtil.toBeginningUpperCase(staff.getState()));
        staff.setDistrict(UdpCommonUtil.toBeginningUpperCase(staff.getDistrict()));
        staff.setName(UdpCommonUtil.toBeginningUpperCase(staff.getName()));

        staffToSave.add(staff);
        batchRecordsProcessed++;
      }
    }

    // Save the batch
    if (!staffToSave.isEmpty()) {
      staffRepository.saveAll(staffToSave);
    }

    return batchRecordsProcessed;
  }

  /**
   * Process a single row and return the staff entity
   *
   * @param row           The Excel row
   * @param columnIndices Map of column indices to column names
   * @param mappings      Map of entity field names to Excel column names
   * @param program       The program
   * @param partner       The partner
   * @return The staff entity, or null if the row has no data
   */
  private Staff processRow(Row row, Map<Integer, String> columnIndices, Map<String, String> mappings,
                           Program program, Partner partner, ExcelFileMetaData excelFileMetaData) {
    // Create a new Staff object or find an existing one by staffId if available
    Staff staff = null;
    String staffId = null;

    // First, collect all values from the row into a HashMap
    Map<String, String> columnValues = getColumnValueMappings(row, columnIndices);


    // Check if we have a Staff_ID mapping
    String staffIdExcelColumn = mappings.get("staffId");
    if (staffIdExcelColumn != null && columnValues.containsKey(staffIdExcelColumn)) {
      staffId = columnValues.get(staffIdExcelColumn);
      if (staffId != null && !staffId.isEmpty()) {
        // Try to find an existing staff with this ID
        Optional<Staff> existingStaff = staffRepository
            .findByStaffIdAndProgramIdAndPartnerId(staffId, program.getProgramId(), partner.getPartnerId());
        if (existingStaff.isPresent()) {
          staff = existingStaff.get();
        }
      }
    }

    // If no existing staff found, create a new one
    if (staff == null) {
      staff = Staff.builder()
          .staffId(staffId)
          .programId(program.getProgramId())
          .partnerId(partner.getPartnerId())
          .excelFileMetaData(excelFileMetaData)
          .build();
    }

    mapExcelColumnsToFields(staff, Staff.class, mappings, columnValues, program, partner);

    // Return the staff entity if the row has data
    return !columnValues.isEmpty() ? staff : null;
  }

  @Override
  protected Object customConvertToFieldType(String value, Class<?> targetType, Program program, Partner partner) {
    if (targetType == Centre.class) {
      Optional<Centre> centre = centreRepository.findByCentreIdAndProgramIdAndPartnerId(value, program.getProgramId(), partner.getPartnerId());
      return centre.orElse(null);
    }
    if (targetType == Designation.class) {
      return new Designation(value);
    } else {
      return super.customConvertToFieldType(value, targetType, program, partner);
    }
  }
}
