package org.technoserve.udp.service;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.technoserve.udp.dto.ApiResponse;
import org.technoserve.udp.dto.TeamDTO;
import org.technoserve.udp.entity.common.Status;
import org.technoserve.udp.entity.program.Program;
import org.technoserve.udp.entity.team.Team;
import org.technoserve.udp.exception.ResourceNotFoundException;
import org.technoserve.udp.repository.ProgramRepository;
import org.technoserve.udp.repository.TeamRepository;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class TeamService {

  private final TeamRepository teamRepository;
  private final ProgramRepository programRepository;

  private final ModelMapper modelMapper;

  @Transactional
  public ApiResponse updateTeamsForProgram(Long programId, List<TeamDTO> teamDTOs) {

    Program program = programRepository.findById(programId)
        .orElseThrow(() -> new ResourceNotFoundException("Program not found with ID: " + programId));

    List<Team> existingTeams = teamRepository.findByProgram_ProgramId(programId);
    for (Team team : existingTeams) {
      team.setStatus(Status.DELETED);
    }
    teamRepository.saveAll(existingTeams);
    List<Team> updatedTeams = teamDTOs.stream().map(team -> modelMapper.map(team, Team.class)).toList();
    updatedTeams.forEach(x -> {
      x.setProgram(program);
      x.setStatus(Status.CREATED);
    });
    teamRepository.saveAll(updatedTeams);

    return new ApiResponse("Team updated in program", programId);
  }

  @Transactional
  public List<TeamDTO> getTeamsByProgram(Long programId) {

    programRepository.findById(programId)
        .orElseThrow(() -> new ResourceNotFoundException("Program not found with ID: " + programId));


    List<Team> teams = teamRepository.findByProgram_ProgramId(programId);


    return teams.stream()
        .filter(team -> team.getStatus() == Status.CREATED)
        .map(team -> modelMapper.map(team, TeamDTO.class))
        .sorted(Comparator.comparingLong(TeamDTO::getTeamId))
        .toList();
  }

}
