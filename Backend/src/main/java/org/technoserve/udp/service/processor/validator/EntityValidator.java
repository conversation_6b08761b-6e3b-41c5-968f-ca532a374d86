package org.technoserve.udp.service.processor.validator;

import java.util.Map;
import java.util.Set;

/**
 * Interface for entity validators
 * @param <T> The entity type
 */
public interface EntityValidator<T> {

    /**
     * Validate an entity based on business rules
     *
     * @param entity The entity to validate
     * @param validationErrors Map to store validation errors
     */
    void validate(T entity, Map<String, String> validationErrors);

    /**
     * Check if a field is required
     *
     * @param fieldName The field name
     * @return True if the field is required, false otherwise
     */
    boolean isRequiredField(String fieldName);

    /**
     * Get all required fields for this entity
     *
     * @return Set of required field names
     */
    Set<String> getRequiredFields();

    /**
     * Validate that all required fields are mapped
     *
     * @param mappings Map of Excel column names to entity field names
     * @param validationErrors Map to store validation errors
     * @return True if all required fields are mapped, false otherwise
     */
    boolean validateRequiredFieldMappings(Map<String, String> mappings, Map<String, String> validationErrors);

    /**
     * Perform custom validations beyond basic field validations
     *
     * @param entity The entity to validate
     * @param validationErrors Map to store validation errors
     */
    void performCustomValidations(T entity, Map<String, String> validationErrors);
}
