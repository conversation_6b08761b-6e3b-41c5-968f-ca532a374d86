package org.technoserve.udp.service.processor;

import org.apache.poi.ss.usermodel.Sheet;
import org.technoserve.udp.dto.ValidationResult;
import org.technoserve.udp.entity.dataflow.ExcelFileMetaData;
import org.technoserve.udp.entity.program.Partner;
import org.technoserve.udp.entity.program.Program;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;

/**
 * Interface for data processors that handle different entity types
 */
public interface DataProcessor {

    /**
     * Process data from an Excel sheet in parallel and insert/update records in the database
     *
     * @param sheet The Excel sheet containing the data
     * @param columnIndices Map of column indices to column names
     * @param mappings Map of Excel column names to entity field names
     * @param program The program
     * @param partner The partner
     * @param batchSize The size of batches to process at once
     * @param executor The executor service to use for parallel processing
     * @return Number of records processed
     */
    int processDataInParallel(Sheet sheet, Map<Integer, String> columnIndices, Map<String, String> mappings,
                              Program program, Partner partner, ExcelFileMetaData excelFileMetaData, int batchSize, ExecutorService executor) throws ExecutionException, InterruptedException;


    /**
     * Validate data from an Excel sheet in parallel without saving to the database
     *
     * @param sheet The Excel sheet containing the data
     * @param columnIndices Map of column indices to column names
     * @param mappings Map of Excel column names to entity field names
     * @param program The program
     * @param partner The partner
     * @param executor The executor service to use for parallel processing
     * @return List of validation results for each row
     */
    List<ValidationResult> validateDataInParallel(Sheet sheet, Map<Integer, String> columnIndices, Map<String, String> mappings,
                                                Program program, Partner partner,ExcelFileMetaData excelFileMetaData, ExecutorService executor) throws ExecutionException, InterruptedException;
}
