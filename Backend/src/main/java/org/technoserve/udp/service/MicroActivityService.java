package org.technoserve.udp.service;

import lombok.RequiredArgsConstructor;
import org.technoserve.udp.dto.ApiResponse;
import org.technoserve.udp.dto.BudgetDTO;
import org.technoserve.udp.dto.BudgetYearWiseDTO;
import org.technoserve.udp.dto.MicroActivityBudgetDTO;
import org.technoserve.udp.entity.common.Status;
import org.technoserve.udp.entity.program.Budget;
import org.technoserve.udp.entity.program.MicroActivity;
import org.technoserve.udp.exception.ConflictException;
import org.technoserve.udp.exception.ResourceNotFoundException;
import org.technoserve.udp.repository.BudgetRepository;
import org.technoserve.udp.repository.MicroActivityRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;

@RequiredArgsConstructor
@Service
public class MicroActivityService {

  private final MicroActivityRepository microActivityRepository;
  private final BudgetRepository budgetRepository;

  public List<MicroActivityBudgetDTO> getFinancialMicroActivities(Long programId) {
    return microActivityRepository.findAllByProgramIdAndTypeFinancial(programId)
        .stream()
        .map(this::convertToDTO)
        .toList();
  }

  @Transactional
  public ApiResponse createBudget(Long microActivityId, BudgetDTO budgetDTO) {
    MicroActivity microActivity = microActivityRepository.findById(microActivityId)
        .orElseThrow(() -> new ResourceNotFoundException("MicroActivity not found"));

    if( microActivity.getBudget() != null && microActivity.getBudget().getBudgetId()!=null) throw new ConflictException("MicroActivity already had budget. Use update");

    Budget budget = convertToEntity(budgetDTO);
    if(budget.getBudgetYearWiseList()!=null) {
      budget.getBudgetYearWiseList().forEach(byw ->{
        byw.setStatus(Status.CREATED);
        byw.setBudget(budget);
    });}

    budget.setMicroActivity(microActivity);
    Budget savedBudget = budgetRepository.save(budget);
    microActivity.setBudget(savedBudget);
    microActivityRepository.save(microActivity);
    return new ApiResponse("Created Budget for Micro activity",savedBudget.getBudgetId());
  }

  @Transactional
  public ApiResponse updateBudget(Long microActivityId, BudgetDTO budgetDTO) {

    MicroActivity microActivity = microActivityRepository.findById(microActivityId)
        .orElseThrow(() -> new ResourceNotFoundException("MicroActivity not found"));

    Budget oldBudget = microActivity.getBudget();

    if(oldBudget.getBudgetId() == null) throw new ResourceNotFoundException("Budget related to this MicroActivity not found");

    if (oldBudget.getBudgetYearWiseList() != null) {
      oldBudget.getBudgetYearWiseList().forEach(byw -> byw.setStatus(Status.DELETED));
      budgetRepository.save(oldBudget);
    }


    Budget newBudget = budgetDTO.toEntity();
    newBudget.setBudgetId(oldBudget.getBudgetId());
    if (newBudget.getBudgetYearWiseList() != null) {
      newBudget.getBudgetYearWiseList().forEach(byw -> {
        byw.setStatus(Status.CREATED);
        byw.setBudget(newBudget);
      });
    }

    microActivity.setBudget(newBudget);

    // Persist the new budget and update the MicroActivity.
    Budget savedBudget = budgetRepository.save(newBudget);
    microActivityRepository.save(microActivity);

    return new ApiResponse("Updated Budget for Micro activity", savedBudget.getBudgetId());
  }

  private MicroActivityBudgetDTO convertToDTO(MicroActivity microActivity) {
    MicroActivityBudgetDTO dto = MicroActivityBudgetDTO.fromEntity(microActivity);
    if (microActivity.getBudget() != null) {
      dto.setBudget(BudgetDTO.fromEntity(microActivity.getBudget()));
      dto.getBudget().setBudgetYearWiseList(
          microActivity.getBudget().getBudgetYearWiseList().stream()
              .filter(byw -> byw.getStatus() == Status.CREATED)
              .map(BudgetYearWiseDTO::fromEntity)
              .sorted(Comparator.comparing(BudgetYearWiseDTO::getBudgetYearWiseId))
              .toList());
    }
    return dto;
  }

  private Budget convertToEntity(BudgetDTO budgetDTO) {
    return budgetDTO.toEntity();
  }
}