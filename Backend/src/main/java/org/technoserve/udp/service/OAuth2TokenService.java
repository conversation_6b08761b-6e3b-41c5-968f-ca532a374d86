package org.technoserve.udp.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClient;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.stereotype.Service;

@Service
public class OAuth2TokenService {

  @Autowired
  private OAuth2AuthorizedClientService authorizedClientService;

  // Method to retrieve the access token for the authenticated user
  public String getAccessToken(OidcUser user) {
    OAuth2AuthorizedClient client = authorizedClientService.loadAuthorizedClient(
        "google", user.getName());
    if (client != null) {
      return client.getAccessToken().getTokenValue();  // Returns the access token as a string
    }
    return null;  // Return null if no client is found
  }
}
