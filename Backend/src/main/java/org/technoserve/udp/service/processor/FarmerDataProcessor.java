package org.technoserve.udp.service.processor;

import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.technoserve.udp.entity.dataflow.Centre;
import org.technoserve.udp.entity.dataflow.ExcelFileMetaData;
import org.technoserve.udp.entity.dataflow.Farmer;
import org.technoserve.udp.entity.program.Partner;
import org.technoserve.udp.entity.program.Program;
import org.technoserve.udp.repository.CentreRepository;
import org.technoserve.udp.repository.FarmerRepository;
import org.technoserve.udp.service.processor.validator.FarmerValidator;
import org.technoserve.udp.util.UdpCommonUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Processor for Farmer data
 */
public class FarmerDataProcessor extends AbstractDataProcessor<Farmer> {

  private final FarmerRepository farmerRepository;
  private final CentreRepository centreRepository;

  public FarmerDataProcessor(FarmerRepository farmerRepository, CentreRepository centreRepository, FormulaEvaluator evaluator) {
    super(new FarmerValidator(centreRepository),evaluator);
    this.farmerRepository = farmerRepository;
    this.centreRepository = centreRepository;
  }

  @Override
  protected Farmer createEntity(Program program, Partner partner, ExcelFileMetaData excelFileMetaData) {
    return Farmer.builder()
        .programId(program.getProgramId())
        .partnerId(partner.getPartnerId())
        .excelFileMetaData(excelFileMetaData)
        .build();
  }

  @Override
  protected void processIdField(Row row, Map<Integer, String> columnIndices, Farmer entity, Map<String, String> validationErrors, Map<String, String> mappings) {
    // Check if we have a Farmer_Id
    String farmerId = null;
    String farmerIdExcelColumn = mappings.get("farmerId");
    Map<String, String> columnValues = getColumnValueMappings(row, columnIndices);

    farmerId = columnValues.get(farmerIdExcelColumn);
    if (farmerId == null || farmerId.isEmpty()) {
      validationErrors.put("Farmer_Id", "Farmer ID is required");
    } else {
      entity.setFarmerId(farmerId);
    }
  }


  /**
   * Process a batch of rows
   *
   * @param sheet         The Excel sheet
   * @param startRow      The starting row index (inclusive)
   * @param endRow        The ending row index (inclusive)
   * @param columnIndices Map of column indices to column names
   * @param mappings      Map of entity field names to Excel column names
   * @param program       The program
   * @param partner       The partner
   * @return Number of records processed in this batch
   */
  @Override
  protected int processBatch(Sheet sheet, int startRow, int endRow, Map<Integer, String> columnIndices,
                             Map<String, String> mappings, Program program, Partner partner, ExcelFileMetaData excelFileMetaData) {
    int batchRecordsProcessed = 0;
    List<Farmer> farmersToSave = new ArrayList<>();

    // Process each row in the batch
    for (int i = startRow; i <= endRow; i++) {
      Row row = sheet.getRow(i);
      if (row == null) continue;

      // Process the row and get the farmer entity
      Farmer farmer = processRow(row, columnIndices, mappings, program, partner, excelFileMetaData);
      // Add to batch if row has data
      if (farmer != null) {
        farmer.setGender(UdpCommonUtil.toBeginningUpperCase(farmer.getGender()));
        farmer.setState(UdpCommonUtil.toBeginningUpperCase(farmer.getState()));
        farmer.setDistrict(UdpCommonUtil.toBeginningUpperCase(farmer.getDistrict()));
        farmer.setVillage(UdpCommonUtil.toBeginningUpperCase(farmer.getVillage()));
        farmer.setMaritalStatus(UdpCommonUtil.toBeginningUpperCase(farmer.getMaritalStatus()));
        farmer.setLandMeasureType(UdpCommonUtil.toBeginningUpperCase(farmer.getLandMeasureType()));
        farmersToSave.add(farmer);
        batchRecordsProcessed++;
      }
    }

    // Save the batch
    if (!farmersToSave.isEmpty()) {
      farmerRepository.saveAll(farmersToSave);
    }

    return batchRecordsProcessed;
  }

  /**
   * Process a single row and return the farmer entity
   *
   * @param row           The Excel row
   * @param columnIndices Map of column indices to column names
   * @param mappings      Map of entity field names to Excel column names
   * @param program       The program
   * @param partner       The partner
   * @return The farmer entity, or null if the row has no data
   */
  private Farmer processRow(Row row, Map<Integer, String> columnIndices, Map<String, String> mappings,
                            Program program, Partner partner, ExcelFileMetaData excelFileMetaData) {
    // Create a new Farmer object or find an existing one by farmerId if available
    Farmer farmer = null;
    String farmerId = null;

    // First, collect all values from the row into a HashMap
    Map<String, String> columnValues = getColumnValueMappings(row, columnIndices);

    // Check if we have a Farmer_Id mapping
    String farmerIdExcelColumn = mappings.get("farmerId");
    if (farmerIdExcelColumn != null && columnValues.containsKey(farmerIdExcelColumn)) {
      farmerId = columnValues.get(farmerIdExcelColumn);
      if (farmerId != null && !farmerId.isEmpty()) {
        // Try to find an existing farmer with this ID
        Optional<Farmer> existingFarmer = farmerRepository
            .findByFarmerIdAndProgramIdAndPartnerId(farmerId, program.getProgramId(), partner.getPartnerId());
        if (existingFarmer.isPresent()) {
          farmer = existingFarmer.get();
        }
      }
    }

    // If no existing farmer found, create a new one
    if (farmer == null) {
      farmer = Farmer.builder()
          .farmerId(farmerId)
          .programId(program.getProgramId())
          .partnerId(partner.getPartnerId())
          .excelFileMetaData(excelFileMetaData)
          .build();
    }

    // Set centreId if it exists in the column values
    String centreIdColumn = mappings.get("centreId");
    if (centreIdColumn != null && columnValues.containsKey(centreIdColumn)) {
      String centreId = columnValues.get(centreIdColumn);
      if (centreId != null && !centreId.isEmpty()) {
        farmer.setCentreId(centreId);
      }
    }

    mapExcelColumnsToFields(farmer, Farmer.class, mappings, columnValues, program, partner);

    // Return the farmer entity if the row has data
    return !columnValues.isEmpty() ? farmer : null;
  }

  @Override
  protected Object customConvertToFieldType(String value, Class<?> targetType,Program program,Partner partner) {
    if (targetType == Centre.class) {
      Optional<Centre> centre = centreRepository.findByCentreIdAndProgramIdAndPartnerId(value,program.getProgramId(),partner.getPartnerId());
      return centre.orElse(null);
    } else {
      return super.customConvertToFieldType(value, targetType,program, partner);
    }
  }

}
