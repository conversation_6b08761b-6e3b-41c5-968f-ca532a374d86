package org.technoserve.udp.service;

import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.technoserve.udp.dto.ApiResponse;
import org.technoserve.udp.dto.PartnerRequest;
import org.technoserve.udp.dto.PartnerResponse;
import org.technoserve.udp.entity.common.Status;
import org.technoserve.udp.entity.program.Partner;
import org.technoserve.udp.exception.ConflictException;
import org.technoserve.udp.exception.ResourceNotFoundException;
import org.technoserve.udp.repository.PartnerRepository;

import java.util.List;

@RequiredArgsConstructor
@Service
public class PartnerService {
  private final PartnerRepository partnerRepository;
  private final ModelMapper modelMapper;

  public List<PartnerResponse> getAllPartners() {
    return partnerRepository.findAllByStatus(Status.CREATED).stream()
        .map(partner -> modelMapper.map(partner, PartnerResponse.class))
        .toList();
  }

  public ApiResponse createPartner(PartnerRequest partnerRequest) {
    if (partnerRepository.existsByNameAndStatus(partnerRequest.getName(), Status.CREATED)) {
      throw new ConflictException("Partner name already exists");
    }
    Partner partner = modelMapper.map(partnerRequest, Partner.class);
    partner.setStatus(Status.CREATED);
    Partner savedPartner = partnerRepository.save(partner);
    return new ApiResponse("Partner created successfully", savedPartner.getPartnerId());
  }

  @Transactional
  public ApiResponse updatePartner(Long partnerId, PartnerRequest partnerRequest) {
    Partner partner = partnerRepository.findById(partnerId)
        .orElseThrow(() -> new ResourceNotFoundException("Partner not found"));

    if (!partner.getName().equals(partnerRequest.getName()) &&
        partnerRepository.existsByNameAndStatus(partnerRequest.getName(), Status.CREATED)) {
      throw new ConflictException("Partner name already exists");
    }

    modelMapper.map(partnerRequest, partner);
    partnerRepository.save(partner); // Ensure persistence
    return new ApiResponse("Partner updated successfully", partner.getPartnerId());
  }

  @Transactional
  public ApiResponse deletePartner(Long partnerId) {
    Partner partner = partnerRepository.findById(partnerId)
        .orElseThrow(() -> new ResourceNotFoundException("Partner not found"));

    partner.setStatus(Status.DELETED);
    partnerRepository.save(partner); // Ensure persistence
    return new ApiResponse("Partner deleted successfully", partnerId);
  }
}
