package org.technoserve.udp.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.technoserve.udp.dto.*;
import org.technoserve.udp.entity.auth.Role;
import org.technoserve.udp.entity.auth.UserEntity;
import org.technoserve.udp.entity.auth.UserStatus;
import org.technoserve.udp.entity.screens.Permissions;
import org.technoserve.udp.entity.screens.PropertyEntity;
import org.technoserve.udp.entity.screens.Screen;
import org.technoserve.udp.entity.valuechain.ValueChain;
import org.technoserve.udp.exception.ConflictException;
import org.technoserve.udp.exception.ResourceNotFoundException;
import org.technoserve.udp.repository.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Service class to handle Role-related operations such as create, fetch, and delete roles.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RoleService {

    private final RoleRepository roleRepository;
    private final UserRepository userRepository;
    private final ModelMapper modelMapper;
    private final PropertyRepository propertyRepository;
    private final PermissionRepository permissionRepository;
    private final ValueChainRepository valueChainRepository;
    private final ScreenRepository screenRepository;

    /**
     * Fetches a Role entity by its name.
     *
     * @param name Name of the role.
     * @return Role entity.
     */
    @Transactional(readOnly = true)
    public Role getRoleByName(String name) {
        log.info("Fetching role with name: {}", name);
        return roleRepository.findByName(name)
                .orElseThrow(() -> {
                    log.error("Role '{}' not found", name);
                    return new ResourceNotFoundException("Role not found: " + name);
                });
    }

    /**
     * Creates?update role along with associated permissions and properties.
     *
     * @param roleDto Role data transfer object.
     * @return Success message if role is created.
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveOrUpdateRole(RoleDto roleDto) {
        try {
            boolean isUpdate = roleDto.getId() != null;
            Role role;

            if (isUpdate) {
                log.info("Attempting to update role: {}", roleDto.getName());
                role = roleRepository.findById(roleDto.getId())
                    .orElseThrow(() -> new ResourceNotFoundException("Role not found with ID: " + roleDto.getId()));

                screenRepository.deleteScreenByRole(role); // delete from DB

                role.setScreens(new ArrayList<>());

            } else {
                log.info("Attempting to create role: {}", roleDto.getName());

                Optional<Role> existingRole = roleRepository.findByName(roleDto.getName());
                if (existingRole.isPresent()) {
                    throw new ConflictException("Role with name '" + roleDto.getName() + "' already exists.");
                }
                role = modelMapper.map(roleDto, Role.class);
            }
            List<Long> valueChainIds = roleDto.getValueChains()
                    .stream()
                    .map(ValueChainResponse::getId)
                    .collect(Collectors.toList());
            // Set accessible value chains
            if(valueChainIds!=null &&valueChainIds.size()>0){
                role.setAccessibleValueChains(valueChainRepository.findAllById(valueChainIds));
            }
            else{
                role.setAccessibleValueChains(null);
            }

            // Map screens from DTO
            List<Screen> screens = roleDto.getScreens().stream().map(screenDto -> {
                Screen screen = modelMapper.map(screenDto, Screen.class);

                List<PropertyEntity> properties = screenDto.getEnabledProperties().stream()
                    .map(property -> propertyRepository.findById(property.getId())
                        .orElseThrow(() -> {
                            log.error("Property not found with ID: {}", property.getId());
                            return new ResourceNotFoundException("Property not found with ID: " + property.getId());
                        }))
                    .toList();

                List<Permissions> permissions = screenDto.getEnabledPermissions().stream()
                    .map(permission -> permissionRepository.findById(permission.getId())
                        .orElseThrow(() -> {
                            log.error("Permission not found with ID: {}", permission.getId());
                            return new ResourceNotFoundException("Permission not found with ID: " + permission.getId());
                        }))
                    .toList();

                screen.setEnabledProperties(properties);
                screen.setEnabledPermissions(permissions);
                screen.setRole(role);

                return screen;
            }).collect(Collectors.toList());

            role.setScreens(screens);
            roleRepository.save(role);

            String action = isUpdate ? "updated" : "created";
            log.info("Role '{}' {} successfully!", roleDto.getName(), action);
            return "Role '" + roleDto.getName() + "' " + action + " successfully!";
        } catch (ResourceNotFoundException e) {
            log.error("Resource not found error: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error occurred while saving role '{}': {}", roleDto.getName(), e.getMessage(), e);
            throw new RuntimeException("Role save failed due to an unexpected error: " + e.getMessage(), e);
        }
    }
    /**
     * Retrieves all roles except those with the ADMIN role.
     *
     * @return List of RoleDto objects.
     */
    @Transactional(readOnly = true)
    public List<RoleDto> getAllRoles() {
        log.info("Fetching all roles (excluding ADMIN)");
        List<Role> roles = roleRepository.findAll().stream()
                .filter(role -> !role.getName().equalsIgnoreCase("ADMIN"))
                .toList();

        List<RoleDto> roleDtos = roles.stream().map(role -> {
            RoleDto roleDto = new RoleDto();
            roleDto.setId(role.getId());
            roleDto.setName(role.getName());
            roleDto.setUpdatedOn(role.getUpdatedOn());
            roleDto.setScreens(null);
            roleDto.setValueChains(null);// Screens are not included in the response
            return roleDto;
        }).toList();

        log.info("Retrieved {} roles", roleDtos.size());
        return roleDtos;
    }
    @Transactional(readOnly = true)
    public List<RoleDto> getAuthScreens(List<String> roles) {
        log.info("Fetching roles for: {}", roles);

        List<Role> rolesList = roleRepository.findByNameIn(roles);

        List<RoleDto> roleDtos = rolesList.stream().map(role -> {
            RoleDto dto = modelMapper.map(role, RoleDto.class);

            // Convert screens to ScreenDto
            List<ScreenDto> screenDtos = role.getScreens().stream()
                    .map(screen -> modelMapper.map(screen, ScreenDto.class))
                    .collect(Collectors.toList());

            dto.setScreens(screenDtos);
            return dto;
        }).collect(Collectors.toList());

        log.info("Retrieved {} roles", roleDtos.size());
        return roleDtos;
    }

    /**
     * Deletes a role by its name if it is not assigned to any user.
     *
     * @param roleName Name of the role to be deleted.
     * @return Success message if deletion is successful.
     */
    @Transactional
    public String deleteRoleByName(String roleName) {
        log.info("Attempting to delete role: {}", roleName);

        // Fetch role or throw exception if not found
        Role role = roleRepository.findByName(roleName)
                .orElseThrow(() -> {
                    log.error("Role '{}' not found", roleName);
                    return new ResourceNotFoundException("Role not found: " + roleName);
                });

        // Check if any user is assigned to this role
        Set<UserEntity> users = userRepository.findByRoles_Name(roleName);
        if (!users.isEmpty()) {
            boolean hasActiveUsers = users.stream()
                    .anyMatch(u -> u.getStatus().equals(UserStatus.ACTIVE));

            if (hasActiveUsers) {
                log.warn("Cannot delete role '{}' because it is assigned to active users", roleName);
                throw new IllegalStateException("Cannot delete role '" + roleName + "' because it is assigned to active users.");
            }
            users.forEach(u->u.setRoles(null));
            userRepository.saveAll(users);
        }

        // Delete the role
        roleRepository.delete(role);
        log.info("Role '{}' deleted successfully", roleName);
        return "Successfully deleted role: " + roleName;
    }

    @Transactional(readOnly = true)
    public Page<RoleDto> getAllRoles(String roleName, Pageable pageable) {
        log.info("Fetching roles with filter: {}, pagination: {}", roleName, pageable);

        // Fetch roles with filtering and pagination
        Page<Role> rolePage;
        if (roleName != null && !roleName.trim().isEmpty()) {
            rolePage = roleRepository.findByNameContainingIgnoreCaseAndNameNot(roleName, "ADMIN", pageable);
        } else {
            rolePage = roleRepository.findByNameNot("ADMIN", pageable);
        }

        modelMapper.typeMap(Role.class, RoleDto.class)
            .addMappings(mapper -> mapper.skip(RoleDto::setValueChains));

        // Convert Role to RoleDto using ModelMapper
        Page<RoleDto> roleDtoPage = rolePage.map(role -> {
            RoleDto roleDto = modelMapper.map(role, RoleDto.class);


            List<ValueChainResponse> valueChains = role.getAccessibleValueChains().stream().map(valueChain -> modelMapper.map(valueChain, ValueChainResponse.class)).toList();

            // Convert Screens
            List<ScreenDto> screenDtos = role.getScreens().stream().map(screen -> {
                ScreenDto screenDto = modelMapper.map(screen, ScreenDto.class);

                // Convert Permissions
                List<PermissionDto> permissionDtos = screen.getEnabledPermissions().stream()
                        .map(permission -> modelMapper.map(permission, PermissionDto.class))
                        .collect(Collectors.toList());

                // Convert Properties
                List<PropertyDto> propertyDtos = screen.getEnabledProperties().stream()
                        .map(property -> modelMapper.map(property, PropertyDto.class))
                        .collect(Collectors.toList());

                screenDto.setEnabledPermissions(permissionDtos);
                screenDto.setEnabledProperties(propertyDtos);

                return screenDto;
            }).collect(Collectors.toList());
            roleDto.setValueChains(valueChains);
            roleDto.setScreens(screenDtos);
            return roleDto;
        });

        log.info("Retrieved {} roles", roleDtoPage.getTotalElements());
        return roleDtoPage;
    }
}
