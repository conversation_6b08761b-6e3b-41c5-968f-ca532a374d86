package org.technoserve.udp.service;

import lombok.RequiredArgsConstructor;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.client.oidc.userinfo.OidcUserRequest;
import org.springframework.security.oauth2.client.oidc.userinfo.OidcUserService;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.oidc.user.DefaultOidcUser;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.stereotype.Service;

import org.technoserve.udp.repository.UserRepository;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

@Service
public class CustomOidcUserService extends OidcUserService {


  private final CustomUserRoleService customUserRoleService; // your service to fetch custom roles

  private final UserRepository userRepository;

  public CustomOidcUserService(CustomUserRoleService customUserRoleService, UserRepository userRepository) {
    this.customUserRoleService = customUserRoleService;
    this.userRepository = userRepository;
  }

  @Override
  public OidcUser loadUser(OidcUserRequest userRequest) throws OAuth2AuthenticationException {
    // Delegate to the default implementation for loading the user
    OidcUser oidcUser = super.loadUser(userRequest);
    // Retrieve additional roles from your application (for example, by email or subject)
    Collection<? extends GrantedAuthority> customAuthorities =
        customUserRoleService.getAuthorities(oidcUser.getEmail());

    // Merge the authorities: default ones plus your custom roles
   // Set<GrantedAuthority> mappedAuthorities = new HashSet<>(oidcUser.getAuthorities());
    Set<GrantedAuthority> mappedAuthorities = new HashSet<>(customAuthorities);
  // mappedAuthorities.addAll(customAuthorities);

    // Return a new OidcUser with the enriched authorities
    return new DefaultOidcUser(mappedAuthorities, oidcUser.getIdToken(), oidcUser.getUserInfo(), "email");
  }
}

