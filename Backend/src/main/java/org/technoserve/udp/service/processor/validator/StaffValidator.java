package org.technoserve.udp.service.processor.validator;

import org.technoserve.udp.entity.dataflow.Staff;
import org.technoserve.udp.repository.CentreRepository;
import org.technoserve.udp.repository.DesignationRepository;
import org.technoserve.udp.util.UdpCommonUtil;
import org.technoserve.udp.util.UdpConstants;

import java.util.Map;
import java.util.Set;

/**
 * Validator for Staff entities
 */
public class StaffValidator extends AbstractEntityValidator<Staff> {

  private final CentreRepository centreRepository;
  private final DesignationRepository designationRepository;

  public StaffValidator(CentreRepository centreRepository, DesignationRepository designationRepository) {
    this.centreRepository = centreRepository;
    this.designationRepository = designationRepository;
  }


  @Override
  public void validate(Staff staff, Map<String, String> validationErrors) {
    // Basic field validations
    if (staff.getStaffId() == null || staff.getStaffId().isEmpty()) {
      validationErrors.put("Staff_ID", "Staff ID is required");
    }

    if (staff.getDesignation() == null || staff.getDesignation().getName().isEmpty()) {
      validationErrors.put("Designation", "Designation is required");
    }

    if (staff.getName() == null || staff.getName().isEmpty()) {
      validationErrors.put("Name", "Name is required");
    }

    if (staff.getGender() == null || staff.getGender().isEmpty()) {
      validationErrors.put("Gender", "Gender is required");
    }

    if (staff.getCountryCode() == null || staff.getCountryCode().isEmpty()) {
      validationErrors.put("Country Code", "Country Code is required");
    }

    if (staff.getMobileNumber() == null || staff.getMobileNumber().isEmpty()) {
      validationErrors.put("Mobile Number", "Mobile Number is required");
    }

    if (staff.getCentreId() == null) {
      validationErrors.put("Centre ID", "Centre ID is required");
    }

    if (staff.getDistrict() == null || staff.getDistrict().isEmpty()) {
      validationErrors.put("District", "District is required");
    }

    if (staff.getState() == null || staff.getState().isEmpty()) {
      validationErrors.put("State", "State is required");
    }
  }

  @Override
  public void performCustomValidations(Staff staff, Map<String, String> validationErrors) {
    // Validate gender format
    if (staff.getGender() != null && !staff.getGender().isEmpty()) {
      String gender = staff.getGender().trim().toUpperCase();
      if (!gender.equals("MALE") && !gender.equals("FEMALE") && !gender.equals("OTHER")) {
        validationErrors.put("Gender", "Gender must be MALE, FEMALE, or OTHER");
      }
    }

    // Validate mobile number format
    if (staff.getCountryCode() != null && !staff.getCountryCode().isEmpty() &&!UdpCommonUtil.isValidLong(staff.getCountryCode())) {
      validationErrors.put("Country Code", "Country Code is not valid");
    }

    // Validate mobile number format
    if (staff.getMobileNumber() != null && !staff.getMobileNumber().isEmpty() &&!UdpCommonUtil.isValidLong(staff.getMobileNumber())) {
        validationErrors.put("Mobile Number", "Mobile Number is not valid");
    }

    // Validate designation from allowed values if needed
    if (staff.getDesignation() != null && !staff.getDesignation().getName().isEmpty()) {
      boolean designationExist = designationRepository.existsByName(staff.getDesignation().getName());
      if (!designationExist) {
        validationErrors.put("Designation", "Designation does not exist in the database ");
      }
    }

    // Validate that the Centre ID exists in the database
    if (staff.getCentreId() != null) {
      boolean centreExists = centreRepository.findByCentreIdAndProgramIdAndPartnerId(staff.getCentreId(),staff.getProgramId(),staff.getPartnerId()).isPresent();
      if (!centreExists) {
        validationErrors.put("Centre ID", "Centre ID does not exist in the database ");
      }
    }
  }

  @Override
  public boolean isRequiredField(String fieldName) {
    return UdpConstants.STAFF_REQUIRED_FIELDS.contains(fieldName);
  }

  @Override
  public Set<String> getRequiredFields() {
    return UdpConstants.STAFF_REQUIRED_FIELDS;
  }
}
