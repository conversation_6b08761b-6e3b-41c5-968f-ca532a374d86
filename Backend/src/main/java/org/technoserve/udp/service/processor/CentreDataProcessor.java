package org.technoserve.udp.service.processor;

import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.technoserve.udp.entity.dataflow.Centre;
import org.technoserve.udp.entity.dataflow.CentreType;
import org.technoserve.udp.entity.dataflow.ExcelFileMetaData;
import org.technoserve.udp.entity.program.Partner;
import org.technoserve.udp.entity.program.Program;
import org.technoserve.udp.repository.CentreRepository;
import org.technoserve.udp.repository.CentreTypeRepository;
import org.technoserve.udp.service.processor.validator.CentreValidator;
import org.technoserve.udp.util.UdpCommonUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Processor for Centre data
 */
public class CentreDataProcessor extends AbstractDataProcessor<Centre> {

  private final CentreRepository centreRepository;

  public CentreDataProcessor(CentreRepository centreRepository, CentreTypeRepository centreTypeRepository, FormulaEvaluator evaluator) {
    super(new CentreValidator(centreTypeRepository),evaluator);
    this.centreRepository = centreRepository;
  }

  @Override
  protected Centre createEntity(Program program, Partner partner, ExcelFileMetaData excelFileMetaData) {
    return Centre.builder()
        .programId(program.getProgramId())
        .partnerId(partner.getPartnerId())
        .excelFileMetaData(excelFileMetaData)
        .build();
  }

  @Override
  protected void processIdField(Row row, Map<Integer, String> columnIndices, Centre entity, Map<String, String> validationErrors, Map<String, String> mappings) {
    // Check if we have a Centre_Id
    String centreId = null;
    String centreIdExcelColumn = mappings.get("centreId");
    Map<String, String> columnValues = getColumnValueMappings(row, columnIndices);

    centreId = columnValues.get(centreIdExcelColumn);
    if (centreId == null || centreId.isEmpty()) {
      validationErrors.put("Centre_Id", "Centre ID is required");
    } else {
      entity.setCentreId(centreId);
    }
  }


  /**
   * Process a batch of rows
   *
   * @param sheet         The Excel sheet
   * @param startRow      The starting row index (inclusive)
   * @param endRow        The ending row index (inclusive)
   * @param columnIndices Map of column indices to column names
   * @param mappings      Map of entity field names to Excel column names
   * @param program       The program
   * @param partner       The partner
   * @return Number of records processed in this batch
   */
  @Override
  protected int processBatch(Sheet sheet, int startRow, int endRow, Map<Integer, String> columnIndices,
                             Map<String, String> mappings, Program program, Partner partner, ExcelFileMetaData excelFileMetaData) {
    int batchRecordsProcessed = 0;
    List<Centre> centresToSave = new ArrayList<>();

    // Process each row in the batch
    for (int i = startRow; i <= endRow; i++) {
      Row row = sheet.getRow(i);
      if (row == null) continue;

      // Process the row and get the centre entity
      Centre centre = processRow(row, columnIndices, mappings, program, partner, excelFileMetaData);

      // Add to batch if row has data
      if (centre != null) {
        // Apply any transformations to the data
        centre.setState(UdpCommonUtil.toBeginningUpperCase(centre.getState()));
        centre.setDistrict(UdpCommonUtil.toBeginningUpperCase(centre.getDistrict()));
        centre.setTaluk(UdpCommonUtil.toBeginningUpperCase(centre.getTaluk()));
        centre.setVillage(UdpCommonUtil.toBeginningUpperCase(centre.getVillage()));

        // Normalize Centre Type to uppercase
        if (centre.getCentreType() != null && centre.getCentreType().getType() != null && !centre.getCentreType().getType().isEmpty()) {
          String normalizedCentreType = centre.getCentreType().getType().trim().toUpperCase();
          // Always set the normalized value - validation will catch invalid types
          centre.setCentreType(new CentreType(normalizedCentreType));
        }

        centresToSave.add(centre);
        batchRecordsProcessed++;
      }
    }

    // Save the batch
    if (!centresToSave.isEmpty()) {
      centreRepository.saveAll(centresToSave);
    }

    return batchRecordsProcessed;
  }

  /**
   * Process a single row and return the centre entity
   *
   * @param row           The Excel row
   * @param columnIndices Map of column indices to column names
   * @param mappings      Map of entity field names to Excel column names
   * @param program       The program
   * @param partner       The partner
   * @return The centre entity, or null if the row has no data
   */
  private Centre processRow(Row row, Map<Integer, String> columnIndices, Map<String, String> mappings,
                            Program program, Partner partner, ExcelFileMetaData excelFileMetaData) {
    // Create a new Centre object or find an existing one by centreId if available
    Centre centre = null;
    String centreId = null;

    // First, collect all values from the row into a HashMap
    Map<String, String> columnValues = getColumnValueMappings(row, columnIndices);

    // Check if we have a Centre_Id mapping
    String centreIdExcelColumn = mappings.get("centreId");
    if (centreIdExcelColumn != null && columnValues.containsKey(centreIdExcelColumn)) {
      centreId = columnValues.get(centreIdExcelColumn);
      if (centreId != null && !centreId.isEmpty()) {
        // Try to find an existing centre with this ID
        Optional<Centre> existingCentre = centreRepository.findByCentreIdAndProgramIdAndPartnerId(centreId, program.getProgramId(), partner.getPartnerId());
        if (existingCentre.isPresent()) {
          centre = existingCentre.get();
        }
      }
    }

    // If no existing centre found, create a new one
    if (centre == null) {
      centre = Centre.builder()
          .centreId(centreId)
          .programId(program.getProgramId())
          .partnerId(partner.getPartnerId())
          .excelFileMetaData(excelFileMetaData)
          .build();
    }

    mapExcelColumnsToFields(centre, Centre.class, mappings, columnValues, program, partner);

    // Return the centre entity if the row has data
    return !columnValues.isEmpty() ? centre : null;
  }

  @Override
  protected Object customConvertToFieldType(String value, Class<?> targetType, Program program, Partner partner) {
    if (targetType == CentreType.class) {
      return new CentreType(value);
    } else {
      return super.customConvertToFieldType(value,targetType,program,partner);
    }
  }
}
