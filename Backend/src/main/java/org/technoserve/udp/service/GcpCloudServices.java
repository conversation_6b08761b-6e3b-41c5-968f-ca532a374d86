package org.technoserve.udp.service;

import com.google.cloud.storage.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.technoserve.udp.exception.BadRequestException;
import org.technoserve.udp.exception.FileTypeNotSupportedException;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.logging.Logger;

@Service
public class GcpCloudServices {

  private static final Logger logger = Logger.getLogger(GcpCloudServices.class.getName());

  private Storage storage;

  @Value("${gcp.storage.logo.bucket.name}")
  private String logoBucketName;

  @Value("${gcp.storage.file.bucket.name}")
  private String fileBucketName;

  @Value("${gcp.storage.logo.root-url}")
  private String storageLocation;

  private static final Set<String> ALLOWED_LOGO_TYPES = Set.of("svg", "png", "jpg", "jpeg");

  private static final Set<String> ALLOWED_FILE_EXTENSIONS = Set.of("xls", "xlsx", "csv");

  private static final Map<String, String> LOGO_CONTENT_TYPE_MAP = Map.of(
      "svg", "image/svg+xml",
      "png", "image/png",
      "jpg", "image/jpeg",
      "jpeg", "image/jpeg"
  );

  private static final Map<String,String> FILE_CONTENT_TYPE_MAP = Map.of(
      "xls", "application/vnd.ms-excel",
      "xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "csv", "text/csv"
  );

  @PostConstruct
  private void init() {
    try {
      this.storage = StorageOptions.getDefaultInstance().getService();
    } catch (Exception e) {
      throw new IllegalStateException("GCP credentials not found. Please set GOOGLE_APPLICATION_CREDENTIALS", e);
    }
  }

  public String uploadLogo(MultipartFile file) {
    try {
      if (file == null || file.isEmpty()) {
        return null;
      }

      String originalFileName = file.getOriginalFilename().toLowerCase();

      String extension = getFileExtension(originalFileName);
      if (!ALLOWED_LOGO_TYPES.contains(extension)) {
        throw new FileTypeNotSupportedException("File type not supported: " + extension);
      }

      String filename = generateUniqueFileName(extension);
      String contentType = LOGO_CONTENT_TYPE_MAP.getOrDefault(extension, "application/octet-stream");

      BlobId blobId = BlobId.of(logoBucketName, filename);
      BlobInfo blobInfo = BlobInfo.newBuilder(blobId).setContentType(contentType).build();

      storage.create(blobInfo, file.getBytes());
      return String.format("%s/%s/%s", storageLocation, logoBucketName, filename);
    } catch (IOException ex) {
      throw new BadRequestException("Failed to upload logo");
    }
  }

  public String uploadFile(MultipartFile file, String path) {
    try {
      if (file == null || file.isEmpty()) {
        return null;
      }

      String originalFileName = file.getOriginalFilename();
      if (originalFileName == null) {
        throw new BadRequestException("File name is missing.");
      }

      String extension = getFileExtension(originalFileName.toLowerCase());
      if (!ALLOWED_FILE_EXTENSIONS.contains(extension)) {
        throw new FileTypeNotSupportedException("Unsupported file type: " + extension);
      }

      String contentType = FILE_CONTENT_TYPE_MAP.getOrDefault(extension, "application/octet-stream");

      BlobId blobId = BlobId.of(fileBucketName, path);
      BlobInfo blobInfo = BlobInfo.newBuilder(blobId)
          .setContentType(contentType)
          .build();

      storage.create(blobInfo, file.getBytes());

      return path;

    } catch (IOException e) {
      throw new BadRequestException("Error uploading file "+ e.getMessage());
    }
  }

  private String getFileExtension(String filename) {
    int lastIndex = filename.lastIndexOf('.');
    return (lastIndex == -1) ? "" : filename.substring(lastIndex + 1).toLowerCase();
  }

  private String generateUniqueFileName(String extension) {
    return UUID.randomUUID().toString().replaceAll("\\W", "") + "." + extension;
  }

  public void deleteFile(String filePath) {

    try {
      if (filePath == null || !filePath.contains("/")) {
        throw new IllegalArgumentException("Invalid file path: " + filePath);
      }
      String fileName = filePath.substring(filePath.lastIndexOf('/') + 1);
      BlobId blobId = BlobId.of(logoBucketName, fileName);
      boolean deleted = storage.delete(blobId);
      if (!deleted) {
        throw new StorageException(404, "File not found: " + fileName);
      }
    } catch (StorageException e) {
      throw new RuntimeException("Error deleting file", e);
    }
  }

  /**
   * Upload a ByteArrayResource to GCP Storage
   *
   * @param resource The ByteArrayResource to upload
   * @param path The path where the file should be stored
   * @return The path of the uploaded file
   */
  public String uploadFile(ByteArrayResource resource, String path) {
    try {
      if (resource == null || resource.contentLength() == 0) {
        logger.warning("Empty resource provided");
        return null;
      }

      logger.info("Uploading resource to path: " + path);

      String extension = getFileExtension(path.toLowerCase());
      String contentType = FILE_CONTENT_TYPE_MAP.getOrDefault(extension, "application/octet-stream");

      BlobId blobId = BlobId.of(fileBucketName, path);
      BlobInfo blobInfo = BlobInfo.newBuilder(blobId).setContentType(contentType).build();

      logger.fine("Uploading to bucket: " + fileBucketName + " with blob name: " + path);
      storage.create(blobInfo, resource.getInputStream().readAllBytes());
      logger.info("Successfully uploaded resource to GCP Storage");

      return path;
    } catch (IOException e) {
      logger.severe("Error uploading resource: " + e.getMessage());
      throw new BadRequestException("Error uploading resource: "+ e.getMessage());
    }
  }

  /**
   * Get a blob from GCP Storage
   *
   * @param path The path of the blob
   * @return The blob, or null if not found
   */
  public Blob getFileBlob(String path) {
    try {
      if (path == null || path.isEmpty()) {
        logger.warning("Empty path provided");
        return null;
      }

      logger.info("Getting blob from path: " + path);

      BlobId blobId = BlobId.of(fileBucketName, path);
      Blob blob = storage.get(blobId);

      if (blob == null || !blob.exists()) {
        logger.warning("Blob not found at path: " + path);
        return null;
      }

      return blob;
    } catch (StorageException e) {
      logger.severe("Error getting blob: " + e.getMessage());
      throw new RuntimeException("Error getting blob: " + e.getMessage(), e);
    }
  }

  public byte[] downloadExcelFile(String path) {
    try {
      if (path == null || path.isEmpty()) {
        throw new BadRequestException("Invalid file path");
      }

      BlobId blobId = BlobId.of(fileBucketName, path);
      Blob blob = storage.get(blobId);

      if (blob == null || !blob.exists()) {
        throw new BadRequestException("File not found in GCP: " + path);
      }

      return blob.getContent(); // Download content as byte array
    } catch (StorageException e) {
      throw new RuntimeException("Failed to download file: " + e.getMessage(), e);
    }
  }
}