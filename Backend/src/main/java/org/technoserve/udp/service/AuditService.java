package org.technoserve.udp.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.stereotype.Service;
import org.technoserve.udp.entity.audit.ChangeLog;

import java.time.LocalDateTime;
import java.util.Optional;


@Service
@RequiredArgsConstructor
public class AuditService {

  private final ObjectMapper objectMapper;

  @PersistenceContext
  private EntityManager entityManager;

  public void logChange(Object entity, String action) {
    try {
      if (!isAuditable(entity)) return;

      ChangeLog changeLog = new ChangeLog();
      changeLog.setEntityName(entity.getClass().getSimpleName());
      changeLog.setAction(action);
      changeLog.setModifiedAt(LocalDateTime.now());
      changeLog.setModifiedBy(getCurrentUser());

      // Extract entity ID
      changeLog.setEntityId(Optional.ofNullable(getEntityId(entity))
          .map(Object::toString)
          .orElse("UNKNOWN"));

      // Convert entity to JSON for tracking
      changeLog.setChanges(objectMapper.writeValueAsString(entity));

      entityManager.persist(changeLog); // Save changeLog to DB
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  private boolean isAuditable(Object entity) {
    return entity.getClass().isAnnotationPresent(org.technoserve.udp.entity.audit.Auditable.class);
  }

  private Object getEntityId(Object entity) {
    try {
      for (var field : entity.getClass().getDeclaredFields()) {
        if (field.isAnnotationPresent(jakarta.persistence.Id.class)) {
          field.setAccessible(true);
          return field.get(entity);
        }
      }
    } catch (IllegalAccessException ignored) {
    }
    return null;
  }

  private String getCurrentUser() {
    return Optional.ofNullable(SecurityContextHolder.getContext().getAuthentication())
        .map(authentication -> ((OidcUser) authentication.getPrincipal()).getEmail())
        .orElse("system");
  }
}
