package org.technoserve.udp.service;

import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.stereotype.Service;
import org.technoserve.udp.dto.ApiResponse;
import org.technoserve.udp.dto.ValueChainRequest;
import org.technoserve.udp.dto.ValueChainResponse;
import org.technoserve.udp.entity.auth.Role;
import org.technoserve.udp.entity.common.Status;
import org.technoserve.udp.entity.valuechain.ValueChain;
import org.technoserve.udp.entity.valuechain.ValueChainType;
import org.technoserve.udp.exception.BadRequestException;
import org.technoserve.udp.exception.ConflictException;
import org.technoserve.udp.exception.ResourceNotFoundException;
import org.technoserve.udp.repository.RoleRepository;
import org.technoserve.udp.repository.ValueChainRepository;
import org.technoserve.udp.repository.ValueChainTypeRepository;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class ValueChainService {

  private final ValueChainRepository valueChainRepository;
  private final ValueChainTypeRepository valueChainTypeRepository;
  private final RoleRepository roleRepository;
  private final ModelMapper modelMapper;

  /**
   * Creates a new ValueChain.
   */
  public ApiResponse createValueChain(ValueChainRequest requestDTO) {
    if (valueChainRepository.existsByNameAndStatus(requestDTO.getName(), Status.CREATED)) {
      throw new ConflictException("Value chain with the given name already exists.");
    }
    valueChainTypeRepository.findById(requestDTO.getType()).orElseThrow(() -> new BadRequestException("Invalid Type"));

    ValueChain valueChain = modelMapper.map(requestDTO, ValueChain.class);
    valueChain.setStatus(Status.CREATED);
    valueChainRepository.save(valueChain);

    return new ApiResponse("Value chain created successfully.", valueChain.getValueChainId());
  }

  /**
   * Updates an existing ValueChain.
   */
  public ApiResponse updateValueChain(Long id, ValueChainRequest requestDTO) {
    ValueChain existingValueChain = valueChainRepository.findByValueChainIdAndStatus(id, Status.CREATED)
        .orElseThrow(() -> new ResourceNotFoundException("ValueChain not found."));

    if (!existingValueChain.getName().equals(requestDTO.getName()) &&
        valueChainRepository.existsByNameAndStatus(requestDTO.getName(), Status.CREATED)) {
      throw new ConflictException("Value chain with the given name already exists.");
    }

    valueChainTypeRepository.findById(requestDTO.getType()).orElseThrow(() -> new BadRequestException("Invalid Type"));


    modelMapper.map(requestDTO, existingValueChain);
    valueChainRepository.save(existingValueChain);

    return new ApiResponse("Value chain updated successfully.", existingValueChain.getValueChainId());
  }

  /**
   * Soft Deletes a ValueChain.
   */
  public ApiResponse deleteValueChain(Long id) {
    ValueChain valueChain = valueChainRepository.findByValueChainIdAndStatus(id, Status.CREATED)
        .orElseThrow(() -> new ResourceNotFoundException("ValueChain not found."));

    valueChain.setStatus(Status.DELETED);
    valueChainRepository.save(valueChain);

    return new ApiResponse("Value chain deleted successfully.", id.toString());
  }

  /**
   * Retrieves all non-deleted ValueChains applicable to role.
   */

  public List<ValueChainResponse> listValueChainsAccessedByRole(OidcUser oidcUser) {
    // Extract role names from authorities (like ROLE_ADMIN, ROLE_USER)
    List<String> roleNames = oidcUser.getAuthorities().stream()
        .map(GrantedAuthority::getAuthority)
        .map(role -> role.startsWith("ROLE_") ? role.substring(5) : role) // remove "ROLE_" prefix
        .toList();

    // Fetch Role entities from DB
    List<Role> roles = roleRepository.findByNameIn(roleNames);

    // Collect all accessible ValueChains from these roles
    Set<ValueChain> accessibleValueChains = roles.stream()
        .flatMap(role -> role.getAccessibleValueChains().stream())
        .filter(valueChain -> valueChain.getStatus() == Status.CREATED)
        .collect(Collectors.toSet()); // Use Set to avoid duplicates


    if (!accessibleValueChains.isEmpty()) {
      //  Map to response
      return accessibleValueChains.stream()
          .map(valueChain -> modelMapper.map(valueChain, ValueChainResponse.class))
          .sorted((x1,x2) -> x1.getName().compareToIgnoreCase(x2.getName())).toList();
    } else {
      return valueChainRepository.findAllByStatus(Status.CREATED).stream()
          .map(valueChain -> modelMapper.map(valueChain, ValueChainResponse.class))
          .sorted((x1,x2) -> x1.getName().compareToIgnoreCase(x2.getName())).toList();
    }
  }


  /**
   * Retrieves all non-deleted ValueChains.
   */
  public List<ValueChainResponse> listAllValueChains() {
    return valueChainRepository.findAllByStatus(Status.CREATED).stream()
        .map(valueChain -> modelMapper.map(valueChain, ValueChainResponse.class))
        .sorted((x1,x2) -> x1.getName().compareToIgnoreCase(x2.getName())).toList();
  }

  public List<ValueChainType> listOfValueChainTypes() {
    return valueChainTypeRepository.findAll();
  }
}
