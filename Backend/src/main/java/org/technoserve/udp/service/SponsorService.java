package org.technoserve.udp.service;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.technoserve.udp.dto.ApiResponse;
import org.technoserve.udp.dto.SponsorRequest;
import org.technoserve.udp.dto.SponsorResponse;
import org.technoserve.udp.entity.common.Status;
import org.technoserve.udp.entity.sponsor.Sponsor;
import org.technoserve.udp.exception.ConflictException;
import org.technoserve.udp.exception.ResourceNotFoundException;
import org.technoserve.udp.repository.SponsorRepository;

import java.util.List;

@Service
public class SponsorService {
  private final SponsorRepository sponsorRepository;
  private final ModelMapper modelMapper;

  public SponsorService(SponsorRepository sponsorRepository, ModelMapper modelMapper) {
    this.sponsorRepository = sponsorRepository;
    this.modelMapper = modelMapper;
  }

  public List<SponsorResponse> getAllSponsors() {
    return sponsorRepository.findAllByStatus(Status.CREATED).stream()
        .map(sponsor -> modelMapper.map(sponsor, SponsorResponse.class))
        .toList();
  }

  public ApiResponse createSponsor(SponsorRequest sponsorDto) {
    // Check if sponsor name already exists in CREATED status
    if (sponsorRepository.existsByNameAndStatus(sponsorDto.getName(), Status.CREATED)) {
      throw new ConflictException("Sponsor name already exists");
    }
    Sponsor sponsor = modelMapper.map(sponsorDto, Sponsor.class);
    sponsor.setStatus(Status.CREATED);
    Sponsor savedSponsor = sponsorRepository.save(sponsor);
    return new ApiResponse("Sponsor created successfully", savedSponsor.getSponsorId());
  }

  @Transactional
  public ApiResponse updateSponsor(Long sponsorId, SponsorRequest sponsorDto) {
    return sponsorRepository.findById(sponsorId)
        .map(existingSponsor -> {
          // Check if the new name already exists in CREATED status (excluding the current sponsor)
          if (!existingSponsor.getName().equals(sponsorDto.getName()) &&
              sponsorRepository.existsByNameAndStatus(sponsorDto.getName(), Status.CREATED)) {
            throw  new ConflictException("Sponsor name already exists");
          }
          modelMapper.map(sponsorDto, existingSponsor);
          return new ApiResponse("Sponsor updated successfully", existingSponsor.getSponsorId());
        })
        .orElseThrow(() -> new ResourceNotFoundException("Sponsor not found"));
  }

  @Transactional
  public ApiResponse deleteSponsor(Long sponsorId) {
    return sponsorRepository.findById(sponsorId)
        .map(sponsor -> {
          sponsor.setStatus(Status.DELETED);
          return new ApiResponse("Sponsor deleted successfully", sponsorId);
        })
        .orElseThrow(() -> new ResourceNotFoundException("Sponsor not found"));
  }
}
