package org.technoserve.udp.service.schedule;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.technoserve.udp.entity.common.Status;
import org.technoserve.udp.entity.program.Program;
import org.technoserve.udp.entity.program.ProgramFactSheet;
import org.technoserve.udp.entity.program.ProgramPhase;
import org.technoserve.udp.repository.ProgramFactSheetRepository;
import org.technoserve.udp.repository.ProgramRepository;

import java.time.LocalDate;
import java.util.List;

/**
 * Service to automatically update program phases based on start and end dates.
 * Runs twice a day to check and update program phases.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProgramPhaseSchedulerService {

    private final ProgramFactSheetRepository programFactSheetRepository;
    private final ProgramRepository programRepository;

    /**
     * Scheduled task that runs twice a day (at 00:01 AM and 12:01 PM) to check program dates
     * and update their phases accordingly.
     * 
     * If startYear equals current date and program status is APPROVED, program phase is set to ACTIVE.
     * If endYear equals current date minus 1 day, program phase is set to INACTIVE.
     */
    @Scheduled(cron = "0 1 0,12 * * *") // Runs at 00:01 AM and 12:01 PM every day
    @Transactional
    public void updateProgramPhases() {
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);
        log.info("Running scheduled program phase update check for date: {}", today);
        
        // Find programs with start date = today and status = APPROVED, then update to ACTIVE
        updateProgramsStartingToday(today);
        
        // Find programs with end date = yesterday and update to INACTIVE
        updateProgramsEndingYesterday(yesterday);
    }
    
    /**
     * Updates programs that are starting today and have APPROVED status to ACTIVE phase.
     * 
     * @param today The current date
     */
    private void updateProgramsStartingToday(LocalDate today) {
        List<ProgramFactSheet> startingPrograms = programFactSheetRepository.findByStartYearAndProgram_Status(today, Status.APPROVED);
        
        if (!startingPrograms.isEmpty()) {
            log.info("Found {} approved programs starting today. Setting phase to ACTIVE.", startingPrograms.size());
            
            for (ProgramFactSheet factSheet : startingPrograms) {
                Program program = factSheet.getProgram();
                log.info("Updating program ID: {}, name: {} to ACTIVE phase", 
                        program.getProgramId(), program.getName());
                factSheet.setProgramPhase(ProgramPhase.ACTIVE);
                programFactSheetRepository.save(factSheet);
            }
        } else {
            log.info("No approved programs found with start date: {}", today);
        }
    }
    
    /**
     * Updates programs that ended yesterday to INACTIVE phase.
     * 
     * @param yesterday The day before current date
     */
    private void updateProgramsEndingYesterday(LocalDate yesterday) {
        List<ProgramFactSheet> endingPrograms = programFactSheetRepository.findByEndYear(yesterday);
        
        if (!endingPrograms.isEmpty()) {
            log.info("Found {} programs that ended yesterday. Setting phase to INACTIVE.", endingPrograms.size());
            
            for (ProgramFactSheet factSheet : endingPrograms) {
                Program program = factSheet.getProgram();
                log.info("Updating program ID: {}, name: {} to INACTIVE phase", 
                        program.getProgramId(), program.getName());
                factSheet.setProgramPhase(ProgramPhase.INACTIVE);
                programFactSheetRepository.save(factSheet);
            }
        } else {
            log.info("No programs found with end date: {}", yesterday);
        }
    }
}
