package org.technoserve.udp.service.processor.validator;

import org.technoserve.udp.entity.dataflow.Farmer;
import org.technoserve.udp.repository.CentreRepository;
import org.technoserve.udp.util.UdpCommonUtil;
import org.technoserve.udp.util.UdpConstants;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * Validator for Farmer entities
 */
public class FarmerValidator extends AbstractEntityValidator<Farmer> {

  private final CentreRepository centreRepository;

  public FarmerValidator(CentreRepository centreRepository) {
    this.centreRepository = centreRepository;
  }

  @Override
  public void validate(Farmer farmer, Map<String, String> validationErrors) {
    // Basic field validations
    if (farmer.getFarmerId() == null || farmer.getFarmerId().isEmpty()) {
      validationErrors.put("Farmer Id", "Farmer ID is required");
    }

    if (farmer.getFarmerName() == null || farmer.getFarmerName().isEmpty()) {
      validationErrors.put("Farmer Name", "Farmer Name is required");
    }

    if (farmer.getGender() == null || farmer.getGender().isEmpty()) {
      validationErrors.put("Gender", "Gender is required");
    }

    if (farmer.getState() == null || farmer.getState().isEmpty()) {
      validationErrors.put("State", "State is required");
    }

    if (farmer.getDistrict() == null || farmer.getDistrict().isEmpty()) {
      validationErrors.put("District", "District is required");
    }

    if (farmer.getVillage() == null || farmer.getVillage().isEmpty()) {
      validationErrors.put("Village", "Village is required");
    }

    if (farmer.getCountryCode() == null || farmer.getCountryCode().isEmpty()) {
      validationErrors.put("Country Code", "Country Code is required");
    }

    if (farmer.getMobileNumber() == null || farmer.getMobileNumber().isEmpty()) {
      validationErrors.put("Mobile_Number", "Mobile Number is required");
    }

    if (farmer.getCentreId() == null) {
      validationErrors.put("Centre ID", "Centre ID is required");
    }

    if (farmer.getHouseHoldSize() == null) {
      validationErrors.put("House Hold Size", "House Hold Size is required");
    }

    if (farmer.getLandSizeUnderCultivation() == null) {
      validationErrors.put("Land Size Under Cultivation", "Land Size Under Cultivation is required");
    }
    if (farmer.getLandMeasureType() == null) {
      validationErrors.put("Land Measure Type", "Land Measure Type is required");
    }
    if (farmer.getOrganicStatus() == null) {
      validationErrors.put("Organic Status", "Organic Status is required");
    }
    if (farmer.getHerdSize() == null) {
      validationErrors.put("Herd Size", "Herd Size is required");
    }
    if (farmer.getHouseholdAnnualIncome() == null) {
      validationErrors.put("Household Annual Income", "Household Annual Income is required");
    }
    if (farmer.getAgriculturalAnnualIncome() == null) {
      validationErrors.put("Agricultural Annual Income", "Agricultural Annual Income is required");
    }
    if (farmer.getDairyAnnualIncome() == null) {
      validationErrors.put("Dairy Annual Income", "Dairy Annual Income is required");
    }
    if (farmer.getCropsGrown() == null) {
      validationErrors.put("Crops Grown", "Crops Grown is required");
    }
    if (farmer.getCattleBreedTypes() == null) {
      validationErrors.put("Cattle Breed Types", "Cattle Breed Types is required");
    }


  }

  @Override
  public void performCustomValidations(Farmer farmer, Map<String, String> validationErrors) {
    // Validate gender format
    if (farmer.getGender() != null && !farmer.getGender().isEmpty()) {
      String gender = farmer.getGender().trim().toUpperCase();
      if (!gender.equals("MALE") && !gender.equals("FEMALE") && !gender.equals("OTHERS")) {
        validationErrors.put("Gender", "Gender must be MALE, FEMALE, or OTHERS");
      }
    }

    // Validate age range
    if (farmer.getAge() != null && (farmer.getAge() < 18 || farmer.getAge() >= 100)) {
      validationErrors.put("Age", "Age must be between 17 and 100");
    }

    if (farmer.getCountryCode() != null && !UdpCommonUtil.isValidLong(farmer.getCountryCode())) {
      validationErrors.put("Country Code", "Country Code is not valid");
    }

    if (farmer.getMobileNumber() != null && !UdpCommonUtil.isValidLong(farmer.getMobileNumber())) {
      validationErrors.put("Mobile Number", "Mobile Number is not valid");
    }

    if (farmer.getLandMeasureType() != null && !farmer.getLandMeasureType().isEmpty()) {
      String landMeasureType = farmer.getLandMeasureType().trim().toUpperCase();
      if (!landMeasureType.equals("ACRE")) {
        validationErrors.put("Land Measure Type", "Land Measure Type must be ACRE");
      }
    }

    if (farmer.getAnyOtherIncomeGeneratingActivity() != null && !farmer.getAnyOtherIncomeGeneratingActivity().isEmpty()) {
      String activity = farmer.getAnyOtherIncomeGeneratingActivity().trim().toLowerCase();

      if (!activity.equals("yes") && !activity.equals("no")) {

        validationErrors.put("Any Other Income Generating Activity", "The value must be either 'Yes' or 'No'.");
      }
    }

    BigDecimal householdIncome = Optional.of(farmer).map(Farmer::getHouseholdAnnualIncome).orElse(BigDecimal.ZERO);
    BigDecimal agricultureIncome = Optional.of(farmer).map(Farmer::getAgriculturalAnnualIncome).orElse(BigDecimal.ZERO);
    BigDecimal dairyIncome = Optional.of(farmer).map(Farmer::getDairyAnnualIncome).orElse(BigDecimal.ZERO);
    BigDecimal otherIncome = Optional.of(farmer).map(Farmer::getOtherAnnualIncome).orElse(BigDecimal.ZERO);

    BigDecimal totalIncome = agricultureIncome.add(dairyIncome).add(otherIncome);

    if (householdIncome.compareTo(totalIncome) < 0) {
      validationErrors.put("Household Annual Income",
          "Household Annual Income must be greater than or equal to the sum of Agricultural Annual Income, Dairy Annual Income, and Other Annual Income.");
    }

    // Validate that the Centre ID exists in the database
    if (farmer.getCentreId() != null) {
      boolean centreExists = centreRepository.findByCentreIdAndProgramIdAndPartnerId(
          farmer.getCentreId(), farmer.getProgramId(), farmer.getPartnerId()).isPresent();
      if (!centreExists) {
        validationErrors.put("Centre ID", "Centre ID does not exist in the database ");
      }
    }
    if (farmer.getLatLong() != null && !farmer.getLatLong().isEmpty() && !UdpCommonUtil.validateLatLongPattern(farmer.getLatLong())) {
      validationErrors.put("Latitude and Longitudes", "Latitude and Longitudes is not valid");
    }


  }

  @Override
  public boolean isRequiredField(String fieldName) {
    return UdpConstants.FARMER_REQUIRED_FIELDS.contains(fieldName);
  }

  @Override
  public Set<String> getRequiredFields() {
    return UdpConstants.FARMER_REQUIRED_FIELDS;
  }
}
