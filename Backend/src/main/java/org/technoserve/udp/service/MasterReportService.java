package org.technoserve.udp.service;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.technoserve.udp.dto.CentreReportDto;
import org.technoserve.udp.dto.FarmerReportDto;
import org.technoserve.udp.dto.StaffReportDto;
import org.technoserve.udp.entity.dataflow.Centre;
import org.technoserve.udp.entity.dataflow.Farmer;
import org.technoserve.udp.entity.dataflow.Staff;
import org.technoserve.udp.entity.program.Partner;
import org.technoserve.udp.entity.program.Program;
import org.technoserve.udp.repository.*;

import java.util.*;

@Service
@RequiredArgsConstructor
public class MasterReportService {

  private final FarmerRepository farmerRepository;
  private final CentreRepository centreRepository;
  private final StaffRepository staffRepository;
  private final PartnerRepository partnerRepository;
  private final ProgramRepository programRepository;

  /**
   * Generate farmer report with sorting and pagination
   *
   * @param programId The program ID to filter by (optional)
   * @param partnerId The partner ID to filter by (optional)
   * @param sortBy The field to sort by
   * @param sortDir The sort direction (asc or desc)
   * @param page The page number (0-based)
   * @param size The page size
   * @return Map containing paginated and sorted farmer report DTOs and metadata
   */
  public Map<String, Object> generateFarmerReport(Long programId, Long partnerId,
                                                  String sortBy, String sortDir, int page, int size) {
    // Create a Sort object for the query
    Sort sort = Sort.by(sortBy);
    sort = sortDir.equalsIgnoreCase("desc") ? sort.descending() : sort.ascending();

    // Create a Pageable object with sorting
    Pageable pageable = PageRequest.of(page, size, sort);

    // Get farmers with pagination and sorting directly from the database
    Page<Farmer> farmerPage = farmerRepository.findByProgramIdAndPartnerId(programId, partnerId, pageable);

    // Convert farmers to DTOs
    List<FarmerReportDto> reportDtos = new ArrayList<>();

    for (Farmer farmer : farmerPage.getContent()) {
      // Get centre details if available
      String centreName = null;
      String centreType = null;

      if (farmer.getCentreId() != null) {
        Optional<Centre> centreOpt = centreRepository.findByCentreIdAndProgramIdAndPartnerId(
            farmer.getCentreId(), farmer.getProgramId(), farmer.getPartnerId());

        if (centreOpt.isPresent()) {
          Centre centre = centreOpt.get();
          centreName = centre.getCentreName();
          centreType = centre.getCentreType() != null ? centre.getCentreType().getType() : null;
        }
      }

      // Get partner name
      String partnerName = "Unknown";
      Optional<Partner> partnerOpt = partnerRepository.findById(farmer.getPartnerId());
      if (partnerOpt.isPresent()) {
        partnerName = partnerOpt.get().getName();
      }

      // Get program name
      String programName = "Unknown";
      Optional<Program> programOpt = programRepository.findById(farmer.getProgramId());
      if (programOpt.isPresent()) {
        programName = programOpt.get().getName();
      }

      // Create the report DTO
      FarmerReportDto reportDto = FarmerReportDto.builder()
          .farmerId(farmer.getFarmerId())
          .farmerTracenetCode(farmer.getFarmerTracenetCode())
          .farmerName(farmer.getFarmerName())
          .age(farmer.getAge())
          .gender(farmer.getGender())
          .state(farmer.getState())
          .district(farmer.getDistrict())
          .village(farmer.getVillage())
          .countryCode(farmer.getCountryCode())
          .mobileNumber(farmer.getMobileNumber())
          .centreId(farmer.getCentreId())
          .centreName(centreName)
          .centreType(centreType)
          .maritalStatus(farmer.getMaritalStatus())
          .spouseName(farmer.getSpouseName())
          .caste(farmer.getCaste())
          .highestEducation(farmer.getHighestEducation())
          .houseHoldSize(farmer.getHouseHoldSize())
          .landSizeUnderCultivation(farmer.getLandSizeUnderCultivation())
          .landMeasureType(farmer.getLandMeasureType())
          .organicStatus(farmer.getOrganicStatus())
          .herdSize(farmer.getHerdSize())
          .anyOtherIncomeGeneratingActivity(farmer.getAnyOtherIncomeGeneratingActivity())
          .householdAnnualIncome(farmer.getHouseholdAnnualIncome())
          .agriculturalAnnualIncome(farmer.getAgriculturalAnnualIncome())
          .dairyAnnualIncome(farmer.getDairyAnnualIncome())
          .otherAnnualIncome(farmer.getOtherAnnualIncome())
          .cropsGrown(farmer.getCropsGrown())
          .cattleBreedTypes(farmer.getCattleBreedTypes())
          .loanAmount(farmer.getLoanAmount())
          .agriculturalLoan(farmer.getAgriculturalLoan())
          .dairyLoan(farmer.getDairyLoan())
          .latLong(farmer.getLatLong())
          .partnerName(partnerName)
          .programName(programName)
          .build();

      reportDtos.add(reportDto);
    }

    // Create response with pagination metadata
    Map<String, Object> response = new HashMap<>();
    response.put("content", reportDtos);
    response.put("currentPage", farmerPage.getNumber());
    response.put("totalItems", farmerPage.getTotalElements());
    response.put("totalPages", farmerPage.getTotalPages());
    response.put("size", farmerPage.getSize());
    response.put("sortBy", sortBy);
    response.put("sortDir", sortDir);

    return response;
  }

  /**
   * Generate centre report with sorting and pagination
   *
   * @param programId The program ID to filter by (optional)
   * @param partnerId The partner ID to filter by (optional)
   * @param sortBy The field to sort by
   * @param sortDir The sort direction (asc or desc)
   * @param page The page number (0-based)
   * @param size The page size
   * @return Map containing paginated and sorted centre report DTOs and metadata
   */
  public Map<String, Object> generateCentreReport(Long programId, Long partnerId,
                                                  String sortBy, String sortDir, int page, int size) {
    // Create a Sort object for the query
    Sort sort = Sort.by(sortBy);
    sort = sortDir.equalsIgnoreCase("desc") ? sort.descending() : sort.ascending();

    // Create a Pageable object with sorting
    Pageable pageable = PageRequest.of(page, size, sort);

    // Get centres with pagination and sorting directly from the database
    Page<Centre> centrePage = centreRepository.findByProgramIdAndPartnerId(programId, partnerId, pageable);

    // Convert centres to DTOs
    List<CentreReportDto> reportDtos = new ArrayList<>();

    for (Centre centre : centrePage.getContent()) {
      // Get partner name
      String partnerName = "Unknown";
      Optional<Partner> partnerOpt = partnerRepository.findById(centre.getPartnerId());
      if (partnerOpt.isPresent()) {
        partnerName = partnerOpt.get().getName();
      }

      // Get program name
      String programName = "Unknown";
      Optional<Program> programOpt = programRepository.findById(centre.getProgramId());
      if (programOpt.isPresent()) {
        programName = programOpt.get().getName();
      }

      // Create the report DTO
      CentreReportDto reportDto = CentreReportDto.builder()
          .centreId(centre.getCentreId())
          .centreName(centre.getCentreName())
          .centreType(centre.getCentreType() != null ? centre.getCentreType().getType() : null)
          .routeNo(centre.getRouteNo())
          .routeName(centre.getRouteName())
          .facilitatorName(centre.getFacilitatorName())
          .facilitatorId(centre.getFacilitatorId())
          .facilitatorCountryCode(centre.getFacilitatorCountryCode())
          .facilitatorMobileNumber(centre.getFacilitatorMobileNumber())
          .state(centre.getState())
          .district(centre.getDistrict())
          .taluk(centre.getTaluk())
          .licenseCertificationType(centre.getLicenseCertificationType())
          .licenseCertificationStatus(centre.getLicenseCertificationStatus())
          .licenseCertificationNo(centre.getLicenseCertificationNo())
          .village(centre.getVillage())
          .latitude(centre.getLatitude())
          .longitude(centre.getLongitude())
          .installedCapacity(centre.getInstalledCapacity())
          .icsType(centre.getIcsType())
          .partnerName(partnerName)
          .programName(programName)
          .build();

      reportDtos.add(reportDto);
    }

    // Create response with pagination metadata
    Map<String, Object> response = new HashMap<>();
    response.put("content", reportDtos);
    response.put("currentPage", centrePage.getNumber());
    response.put("totalItems", centrePage.getTotalElements());
    response.put("totalPages", centrePage.getTotalPages());
    response.put("size", centrePage.getSize());
    response.put("sortBy", sortBy);
    response.put("sortDir", sortDir);

    return response;
  }

  /**
   * Generate staff report with sorting and pagination
   *
   * @param programId The program ID to filter by (optional)
   * @param partnerId The partner ID to filter by (optional)
   * @param sortBy The field to sort by
   * @param sortDir The sort direction (asc or desc)
   * @param page The page number (0-based)
   * @param size The page size
   * @return Map containing paginated and sorted staff report DTOs and metadata
   */
  public Map<String, Object> generateStaffReport(Long programId, Long partnerId,
                                                 String sortBy, String sortDir, int page, int size) {
    // Create a Sort object for the query
    Sort sort = Sort.by(sortBy);
    sort = sortDir.equalsIgnoreCase("desc") ? sort.descending() : sort.ascending();

    // Create a Pageable object with sorting
    Pageable pageable = PageRequest.of(page, size, sort);

    // Get staff with pagination and sorting directly from the database
    Page<Staff> staffPage = staffRepository.findByProgramIdAndPartnerId(programId, partnerId, pageable);

    // Convert staff to DTOs
    List<StaffReportDto> reportDtos = new ArrayList<>();

    for (Staff staff : staffPage.getContent()) {
      // Get centre details if available
      String centreName = null;
      String centreType = null;

      if (staff.getCentreId() != null) {
        Optional<Centre> centreOpt = centreRepository.findByCentreIdAndProgramIdAndPartnerId(
            staff.getCentreId(), staff.getProgramId(), staff.getPartnerId());

        if (centreOpt.isPresent()) {
          Centre centre = centreOpt.get();
          centreName = centre.getCentreName();
          centreType = centre.getCentreType() != null ? centre.getCentreType().getType() : null;
        }
      }

      // Get partner name
      String partnerName = "Unknown";
      Optional<Partner> partnerOpt = partnerRepository.findById(staff.getPartnerId());
      if (partnerOpt.isPresent()) {
        partnerName = partnerOpt.get().getName();
      }

      // Get program name
      String programName = "Unknown";
      Optional<Program> programOpt = programRepository.findById(staff.getProgramId());
      if (programOpt.isPresent()) {
        programName = programOpt.get().getName();
      }

      // Create the report DTO
      StaffReportDto reportDto = StaffReportDto.builder()
          .staffId(staff.getStaffId())
          .name(staff.getName())
          .designation(staff.getDesignation() != null ? staff.getDesignation().getName() : null)
          .gender(staff.getGender())
          .countryCode(staff.getCountryCode())
          .mobileNumber(staff.getMobileNumber())
          .centreId(staff.getCentreId())
          .centreName(centreName)
          .centreType(centreType)
          .district(staff.getDistrict())
          .state(staff.getState())
          .partnerName(partnerName)
          .programName(programName)
          .build();

      reportDtos.add(reportDto);
    }

    // Create response with pagination metadata
    Map<String, Object> response = new HashMap<>();
    response.put("content", reportDtos);
    response.put("currentPage", staffPage.getNumber());
    response.put("totalItems", staffPage.getTotalElements());
    response.put("totalPages", staffPage.getTotalPages());
    response.put("size", staffPage.getSize());
    response.put("sortBy", sortBy);
    response.put("sortDir", sortDir);

    return response;
  }
}
