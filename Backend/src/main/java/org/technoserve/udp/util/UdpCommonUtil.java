package org.technoserve.udp.util;

import org.technoserve.udp.dto.FieldInfoResponse;
import org.technoserve.udp.entity.common.FieldInfo;


import java.lang.reflect.Field;
import java.util.*;
import java.util.regex.Pattern;

public interface UdpCommonUtil {

  final String BASE_PACKAGE = "org.technoserve.udp.entity.dataflow.";

  static List<FieldInfoResponse> getEntityFields(String entityType) throws ClassNotFoundException {
    List<FieldInfoResponse> fieldInfos = new ArrayList<>();

    Class<?> clazz = Class.forName(BASE_PACKAGE + entityType);

    for (Field field : clazz.getDeclaredFields()) {
      if (field.isAnnotationPresent(FieldInfo.class)) {
        FieldInfo annotation = field.getAnnotation(FieldInfo.class);
        fieldInfos.add(new FieldInfoResponse(field.getName(), annotation.name()));
      }
    }
    return fieldInfos;

  }

  static <K, V> K getKeyFromValue(Map<K, V> map, V value) {
    for (Map.Entry<K, V> entry : map.entrySet()) {
      if (Objects.equals(entry.getValue(), value)) {
        return entry.getKey();
      }
    }
    return null; // not found
  }

   static String toBeginningUpperCase(String input) {
    if (input == null || input.isBlank()) {
      return input;
    }
    String trimmed = input.trim();
    return trimmed.substring(0, 1).toUpperCase() + trimmed.substring(1).toLowerCase();
  }

  static boolean isValidLong(String value) {
    try {
      Long.parseLong(value); // Attempt to parse the string as a Long
      return true; // it's a valid Long
    } catch (NumberFormatException e) {
      return false; // it's not a valid Long
    }


  }
  // Static method to validate the input pattern
   static boolean validateLatLongPattern(String input) {
    // Define the regex pattern to match the required format
    String regex = "\\[\\d+\\.\\d{2},\\d+\\.\\d{2}\\];\\[\\d+\\.\\d{2},\\d+\\.\\d{2}\\]";

    // Check if the input matches the regex pattern
    return Pattern.matches(regex, input);
  }

}
