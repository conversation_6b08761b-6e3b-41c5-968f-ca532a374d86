package org.technoserve.udp.entity.common;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Entity
@Table(name = "master_country")
@Getter
@Setter
@NoArgsConstructor
public class MasterCountry {

  @Id
  @Column(name="country_name")
  private String countryName;

  @OneToMany(mappedBy = "masterCountry")
  @JsonManagedReference
  private List<MasterState> statesList;

}
