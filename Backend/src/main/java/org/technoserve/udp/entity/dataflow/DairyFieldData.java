package org.technoserve.udp.entity.dataflow;

import jakarta.persistence.*;
import lombok.*;
import org.technoserve.udp.entity.common.FieldInfo;

import java.time.LocalDate;

/**
 * Entity representing Dairy Field Data
 */
@Entity
@Table(name = "dairy_field_data")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DairyFieldData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "dairy_field_data_id")
    private Long dairyFieldDataId;
    
    @Column(name = "date")
    @FieldInfo(name = "date")
    private LocalDate date;

    @Column(name = "program_id")
    private Long programId;

    @Column(name = "partner_id")
    private Long partnerId;

    @FieldInfo(name = "Total Farmers")
    @Column(name = "total_farmers")
    private Integer totalFarmers;

    @FieldInfo(name = "No of Animal Welfare Farms")
    @Column(name = "no_of_animal_welfare_farms")
    private Integer noOfAnimalWelfareFarms;

    @FieldInfo(name = "No of Women Empowerment")
    @Column(name = "no_of_women_empowerment")
    private Integer noOfWomenEmpowerment;

    @ManyToOne
    @JoinColumn(name = "excel_file_meta_data_id")
    private ExcelFileMetaData excelFileMetaData;
}
