package org.technoserve.udp.entity.program;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum MicroActivityFrequency {

  DAILY("Daily"),
  WEEKLY("Weekly"),
  TEN_DAYS("10-Days"),
  FORTNIGHTLY("Fortnightly"),
  MONTHL<PERSON>("Monthly"),
  BI_MONTHLY("Bi-Monthly"),
  QUARTERLY("Quarterly"),
  HALF_YEARLY("Half-yearly"),
  SEASONALLY("Seasonally"),
  ANNUALLY("Annually");

  private final String displayValue;

}
