package org.technoserve.udp.entity.screens;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import lombok.*;
import org.technoserve.udp.entity.common.AbstractEntity;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "property", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"name", "master_screen_id"})
})
public class PropertyEntity extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "label_name", nullable = false)
    private String labelName;

    @ManyToOne(fetch =FetchType.LAZY )
    @JoinColumn(name = "master_screen_id")
    @JsonBackReference
    private MasterScreen masterScreen;
}
