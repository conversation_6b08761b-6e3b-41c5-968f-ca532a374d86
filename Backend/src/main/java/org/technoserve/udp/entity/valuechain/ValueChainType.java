package org.technoserve.udp.entity.valuechain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "value_chain_type")
@Getter
@Setter
@NoArgsConstructor
public class ValueChainType {

  @Id
  @Column(name = "type")
  private String type;

}
