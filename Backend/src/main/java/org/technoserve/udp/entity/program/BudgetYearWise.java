package org.technoserve.udp.entity.program;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.technoserve.udp.entity.common.AbstractEntity;
import org.technoserve.udp.entity.common.Status;

import java.math.BigDecimal;

@Entity
@Table(name="budget_year_wise")
@Getter
@Setter
@NoArgsConstructor
public class BudgetYearWise extends AbstractEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "budget_year_wise_id")
  private Long budgetYearWiseId;

  @Column
  private BigDecimal totalAmount;

  @Column
  private BigDecimal ip;

  @Column
  private BigDecimal farmerContribution;

  @Column
  private BigDecimal projectSponsorContribution;

  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "status")
  @JsonIgnore
  private Status status;

  @ManyToOne(cascade = CascadeType.ALL)
  @JoinColumn(name = "budget_id")
  @JsonBackReference
  private Budget budget;

}
