package org.technoserve.udp.entity.audit;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Table(name = "change_log")
@Getter
@Setter
@NoArgsConstructor
public class ChangeLog {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String entityName;
  // Name of the entity (e.g., "Program")
  private String entityId;
  // ID of the modified entity
  private String action;
  // INSERT, UPDATE, DELETE
  private String modifiedBy;
  // User who made the change

  private LocalDateTime modifiedAt; // Timestamp

  @Column(columnDefinition = "TEXT")
  private String changes; // JSON representation of modified fields


}
