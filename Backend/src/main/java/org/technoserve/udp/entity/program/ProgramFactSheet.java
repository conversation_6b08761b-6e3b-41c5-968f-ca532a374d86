package org.technoserve.udp.entity.program;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.technoserve.udp.entity.common.AbstractEntity;

import java.time.LocalDate;
import java.util.List;

@Entity
@Table(name = "program_fact_sheet")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProgramFactSheet extends AbstractEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "program_fact_sheet_id")
  private Long programFactSheetId;


  @ManyToMany(fetch = FetchType.EAGER)
  @JoinTable(
      name = "program_fact_sheet_partners",
      joinColumns = @JoinColumn(name = "program_fact_sheet_id"),
      inverseJoinColumns = @JoinColumn(name = "partner_id")
  )
  private List<Partner> projectPartners;

  @Column
  private int totalFarmers;

  @Column
  private int totalFemaleFarmers;

  @OneToMany(mappedBy = "programFactSheet")
  @JsonManagedReference
  private List<DistrictData> districtWiseData;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
  @Column(name = "start_year", nullable = false)
  private LocalDate startYear;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
  @Column(name = "end_year", nullable = false)
  private LocalDate endYear;

  @Column
  private String programGoal;


  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "program_phase")
  private ProgramPhase programPhase;

  @OneToOne(mappedBy = "programFactSheet")
  @JsonBackReference
  private Program program;

}
