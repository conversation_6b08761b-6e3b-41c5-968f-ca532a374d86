package org.technoserve.udp.entity.program;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum BudgetFrequency {

  DAILY("Daily"),
  WEEKLY("Weekly"),
  TEN_DAYS("10-Days"),
  FORTNIGHTLY("Fortnightly"),
  MONTH<PERSON><PERSON>("Monthly"),
  BI_MONTHLY("Bi-Monthly"),
  QUARTERLY("Quarterly"),
  HALF_YEARLY("Half-yearly"),
  SEASONALLY("Seasonally"),
  ANNUALLY("Annually");

  private final String displayValue;

}
