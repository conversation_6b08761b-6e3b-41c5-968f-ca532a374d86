package org.technoserve.udp.entity.screens;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.*;
import org.technoserve.udp.entity.common.AbstractEntity;

import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "master_screen", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"name"})
})
public class MasterScreen extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @OneToMany(mappedBy = "masterScreen", cascade = CascadeType.ALL)
    @JsonManagedReference
    private List<PropertyEntity> properties;

    @OneToMany(mappedBy = "masterScreen", cascade = CascadeType.ALL)
    @JsonManagedReference
    private List<Permissions> permissions;
}
