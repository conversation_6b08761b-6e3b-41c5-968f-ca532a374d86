package org.technoserve.udp.entity.valuechain;

import com.fasterxml.jackson.annotation.JsonUnwrapped;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.technoserve.udp.entity.audit.Auditable;
import org.technoserve.udp.entity.common.AbstractEntity;
import org.technoserve.udp.entity.common.Status;

@Entity
@Table(name = "value_chain")
@Getter
@Setter
@NoArgsConstructor
@Auditable
public class ValueChain extends AbstractEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name="value_chain_id")
  private Long valueChainId;

  @Column(nullable = false, unique = true)
  private String name;

  @OneToOne
  @JoinColumn(name = "type")
  @JsonUnwrapped
  private  ValueChainType valueChainType;

  @Column
  private String logoURL;

  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "status")
  private Status status;

}



