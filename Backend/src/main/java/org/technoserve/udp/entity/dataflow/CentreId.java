package org.technoserve.udp.entity.dataflow;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Composite primary key for Centre entity
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class CentreId implements Serializable {

    private String centreId;
    private Long programId;
    private Long partnerId;
}
