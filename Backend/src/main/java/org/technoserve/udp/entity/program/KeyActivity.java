package org.technoserve.udp.entity.program;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.technoserve.udp.entity.common.AbstractEntity;
import org.technoserve.udp.entity.common.Status;

import java.util.List;

@Entity
@Table(name = "key_activity")
@Getter
@Setter
@NoArgsConstructor
public class KeyActivity extends AbstractEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "key_activity_id")
  private Long keyActivityId;

  @Column
  private String keyActivityName;


  @OneToMany(mappedBy = "keyActivity", cascade = CascadeType.ALL)
  @JsonManagedReference
  private List<SubActivity> subActivities;

  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "status")
  @JsonIgnore
  private Status status;

  @ManyToOne
  @JoinColumn(name = "output_indicator_id")
  @JsonBackReference
  private OutputIndicator outputIndicator;

}
