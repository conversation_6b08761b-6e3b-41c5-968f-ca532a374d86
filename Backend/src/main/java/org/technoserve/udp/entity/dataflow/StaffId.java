package org.technoserve.udp.entity.dataflow;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Composite primary key for Staff entity
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class StaffId implements Serializable {

    private String staffId;
    private Long programId;
    private Long partnerId;
}
