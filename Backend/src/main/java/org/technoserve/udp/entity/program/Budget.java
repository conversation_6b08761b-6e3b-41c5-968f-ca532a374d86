package org.technoserve.udp.entity.program;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.technoserve.udp.entity.common.AbstractEntity;

import java.math.BigDecimal;
import java.util.List;

@Entity
@Table(name="budget")
@Getter
@Setter
@NoArgsConstructor
public class Budget extends AbstractEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name="budget_id")
  private Long budgetId;

  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "budget_frequency")
  private BudgetFrequency frequency;

  @Column
  private String unit;

  @Column
  private BigDecimal unitCost;

  @Column
  private BigDecimal noOfUnits;

  @Column
  private BigDecimal totalCost;

  @OneToMany(mappedBy = "budget",cascade = CascadeType.ALL)
  @JsonManagedReference
  private List<BudgetYearWise> budgetYearWiseList;

  @OneToOne(mappedBy = "budget")
  @JsonBackReference
  private MicroActivity microActivity;
  
}
