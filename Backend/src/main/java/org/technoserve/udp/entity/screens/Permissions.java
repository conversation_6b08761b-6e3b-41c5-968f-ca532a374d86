package org.technoserve.udp.entity.screens;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.technoserve.udp.entity.common.AbstractEntity;
import org.technoserve.udp.entity.auth.EndPoint;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "permissions", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"name", "master_screen_id"})
})
public class Permissions extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "label_name", nullable = false)
    private String labelName;


    @ManyToMany(cascade = CascadeType.MERGE, fetch = FetchType.EAGER)
    @JoinTable(
            name = "permission_endpoints",
            joinColumns = @JoinColumn(name = "permission_id"),
            inverseJoinColumns = @JoinColumn(name = "endpoint_id")
    )
    private List<EndPoint> endPoints;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "master_screen_id")
    @JsonBackReference
    private MasterScreen masterScreen;

}
