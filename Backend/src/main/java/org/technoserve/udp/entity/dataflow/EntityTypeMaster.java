package org.technoserve.udp.entity.dataflow;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.technoserve.udp.entity.valuechain.ValueChainType;

@Entity
@Table(name = "entity_type_master")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EntityTypeMaster {

  @Id
  @Column(name = "entity_name", unique = true, nullable = false)
  private String entityName;
  
  @Column(name = "display_name", nullable = false)
  private String displayName;

  @Column(name = "value_chain_type")
  private String valueChainType;
  
  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "file_type")
  private FileType fileType;
}
