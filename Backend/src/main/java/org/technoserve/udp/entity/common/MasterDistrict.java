package org.technoserve.udp.entity.common;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "master_district")
@Getter
@Setter
@NoArgsConstructor
public class MasterDistrict {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long masterDistrictId;

  @Column(name="district_name")
  private String districtName;

  @ManyToOne
  @JsonBackReference
  @JoinColumn(name = "master_state_id")
  private MasterState masterState;

}
