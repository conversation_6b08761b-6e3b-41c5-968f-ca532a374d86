package org.technoserve.udp.entity.program;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.technoserve.udp.entity.common.AbstractEntity;
import org.technoserve.udp.entity.common.Status;

import java.util.List;

@Entity
@Table(name = "dimension")
@Getter
@Setter
@NoArgsConstructor
public class Dimension extends AbstractEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "dimension_id")
  private Long dimensionId;

  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "type")
  private DimensionType type;


  @OneToMany(mappedBy = "dimension",cascade = CascadeType.ALL)
  @JsonManagedReference
  private List<Objective> objectives;

  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "status")
  @JsonIgnore
  private Status status;

  @ManyToOne
  @JsonBackReference
  @JoinColumn(name = "program_log_frame_id")
  private ProgramLogFrame programLogFrame;

}
