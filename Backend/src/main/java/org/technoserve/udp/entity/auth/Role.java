package org.technoserve.udp.entity.auth;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.*;
import org.technoserve.udp.entity.common.AbstractEntity;
import org.technoserve.udp.entity.screens.Screen;
import org.technoserve.udp.entity.valuechain.ValueChain;

import java.util.List;
import java.util.Set;
@ToString(exclude = "screens")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "roles", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"name"})
})
public class Role extends AbstractEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(nullable = false)
  private String name;

  @ManyToMany
  @JoinTable(
      name = "role_value_chain",
      joinColumns = @JoinColumn(name = "role_id"),
      inverseJoinColumns = @JoinColumn(name = "value_chain_id")
  )
  private List<ValueChain> accessibleValueChains;

  @OneToMany(mappedBy = "role", fetch = FetchType.LAZY ,cascade = CascadeType.ALL)
  @JsonManagedReference
  private List<Screen> screens;
}
