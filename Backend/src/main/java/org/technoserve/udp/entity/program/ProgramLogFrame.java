package org.technoserve.udp.entity.program;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.technoserve.udp.entity.common.AbstractEntity;

import java.util.List;

@Entity
@Table(name = "program_log_frame")
@Getter
@Setter
@NoArgsConstructor
public class ProgramLogFrame extends AbstractEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name="program_log_frame_id")
  private Long programLogFrameId;

  @OneToMany(mappedBy = "programLogFrame",cascade = CascadeType.ALL)
  @JsonManagedReference
  private List<Dimension> dimensions;

  @OneToOne(mappedBy = "programLogFrame")
  @JsonBackReference
  private Program program;
}
