package org.technoserve.udp.entity.program;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.technoserve.udp.entity.common.AbstractEntity;
import org.technoserve.udp.entity.common.Status;

import java.util.List;

@Entity
@Table(name="sub_activity")
@Getter
@Setter
@NoArgsConstructor
public class SubActivity extends AbstractEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column( name = "sub_activity_id" )
  private Long subActivityId;

  @Column
  private String subActivityName;

  @OneToMany(mappedBy = "subActivity", cascade = CascadeType.ALL)
  @JsonManagedReference
  private List<MicroActivity> microActivities;


  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "status")
  @JsonIgnore
  private Status status;


  @ManyToOne
  @JoinColumn(name = "key_activity_id")
  @JsonBackReference
  private KeyActivity keyActivity;


}
