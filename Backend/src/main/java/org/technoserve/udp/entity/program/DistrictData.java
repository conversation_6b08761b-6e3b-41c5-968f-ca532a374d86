package org.technoserve.udp.entity.program;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import lombok.*;
import org.technoserve.udp.entity.common.AbstractEntity;
import org.technoserve.udp.entity.common.MasterDistrict;

@Entity
@Table(name = "district_data")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DistrictData extends AbstractEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "district_data_id")
  private Long districtDataId;

  @ManyToOne
  @JsonBackReference
  @JoinColumn(name = "master_district_id")
  private MasterDistrict masterDistrict;

  @Column
  private int numberOfVillage;


  @ManyToOne
  @JsonBackReference
  @JoinColumn(name="program_fact_sheet_id")
  private ProgramFactSheet programFactSheet;

}
