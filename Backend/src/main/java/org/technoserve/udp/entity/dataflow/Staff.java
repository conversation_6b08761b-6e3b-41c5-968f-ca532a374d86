package org.technoserve.udp.entity.dataflow;

import com.fasterxml.jackson.annotation.JsonUnwrapped;
import jakarta.persistence.*;
import lombok.*;
import org.technoserve.udp.entity.common.FieldInfo;
import org.technoserve.udp.entity.program.Partner;
import org.technoserve.udp.entity.program.Program;

/**
 * Entity representing a Staff member
 */
@Entity
@Table(name = "staff")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@IdClass(StaffId.class)
public class Staff {

    @Id
    @FieldInfo(name = "Staff ID")
    @Column(name = "staff_id")
    private String staffId;

    @Id
    @Column(name = "program_id")
    private Long programId;

    @Id
    @Column(name = "partner_id")
    private Long partnerId;

    @FieldInfo(name = "Name")
    @Column(name = "name")
    private String name;

    @FieldInfo(name = "Designation")
    @ManyToOne
    @JsonUnwrapped
    @JoinColumn(name = "designation")
    private Designation designation;

    @FieldInfo(name = "Gender")
    @Column(name = "gender")
    private String gender;

    @FieldInfo(name = "Country Code")
    @Column(name = "country_code")
    private String countryCode = "91";

    @FieldInfo(name = "Mobile Number")
    @Column(name = "mobile_number")
    private String mobileNumber;

    @FieldInfo(name="Center ID")
    @Column(name = "centre_id")
    private String centreId;

    @FieldInfo(name = "District")
    @Column(name = "district")
    private String district;

    @FieldInfo(name = "State")
    @Column(name = "state")
    private String state;

    @ManyToOne
    @JoinColumn(name = "excel_file_meta_data_id")
    private ExcelFileMetaData excelFileMetaData;
}
