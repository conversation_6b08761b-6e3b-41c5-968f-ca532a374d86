package org.technoserve.udp.entity.dataflow;

import com.fasterxml.jackson.annotation.JsonUnwrapped;
import jakarta.persistence.*;
import lombok.*;
import org.technoserve.udp.entity.common.FieldInfo;
import org.technoserve.udp.entity.program.Partner;
import org.technoserve.udp.entity.program.Program;

/**
 * Entity representing a Centre (VLCC/BMC/MCC/ICS/FPO/CPG)
 */
@Entity
@Table(name = "centre")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@IdClass(CentreId.class)
public class Centre {

    @Id
    @Column(name = "centre_id")
    @FieldInfo(name = "Centre ID")
    private String centreId;

    @FieldInfo(name = "Centre Name")
    @Column(name = "centre_name")
    private String centreName;

    @FieldInfo(name = "Centre Type")
    @OneToOne
    @JoinColumn(name = "centre_type")
    @JsonUnwrapped
    private CentreType centreType;

    @FieldInfo(name = "Route No")
    @Column(name = "route_no")
    private String routeNo;

    @FieldInfo(name = "Route Name")
    @Column(name = "route_name")
    private String routeName;

    @FieldInfo(name = "Facilitator Name")
    @Column(name = "facilitator_name")
    private String facilitatorName;

    @FieldInfo(name = "Facilitator ID")
    @Column(name = "facilitator_id")
    private String facilitatorId;

    @FieldInfo(name = "Facilitator Country Code")
    @Column(name = "facilitator_country_code")
    private String facilitatorCountryCode = "91";

    @FieldInfo(name = "Facilitator Mobile Number")
    @Column(name = "facilitator_mobile_number")
    private String facilitatorMobileNumber;

    @FieldInfo(name = "State")
    @Column(name = "state")
    private String state;

    @FieldInfo(name = "District")
    @Column(name = "district")
    private String district;

    @FieldInfo(name = "Taluk")
    @Column(name = "taluk")
    private String taluk;

    @FieldInfo(name = "Village")
    @Column(name = "village")
    private String village;

    @FieldInfo(name = "License/Certification Type")
    @Column(name = "license_certification_type")
    private String licenseCertificationType;

    @FieldInfo(name = "License/Certification Status")
    @Column(name = "license_certification_status")
    private String licenseCertificationStatus;

    @FieldInfo(name = "License/Certification No")
    @Column(name = "license_certification_no")
    private String licenseCertificationNo;

    @FieldInfo(name = "Lat")
    @Column(name = "latitude")
    private Float latitude;

    @FieldInfo(name = "Long")
    @Column(name = "longitude")
    private Float longitude;

    @FieldInfo(name = "Installed Capacity")
    @Column(name = "installed_capacity")
    private Integer installedCapacity;

    @FieldInfo(name = "ICS Type")
    @Column(name = "ics_type")
    private String icsType;

    @Id
    @Column(name = "program_id")
    private Long programId;

    @Id
    @Column(name = "partner_id")
    private Long partnerId;

    @ManyToOne
    @JoinColumn(name = "excel_file_meta_data_id")
    private ExcelFileMetaData excelFileMetaData;
}
