package org.technoserve.udp.entity.program;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.technoserve.udp.entity.audit.Auditable;
import org.technoserve.udp.entity.common.AbstractEntity;
import org.technoserve.udp.entity.common.Status;
import org.technoserve.udp.entity.team.Team;
import org.technoserve.udp.entity.valuechain.ValueChain;

import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "program")
@Getter
@Setter
@NoArgsConstructor
@Auditable
public class Program extends AbstractEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name="program_id")
  private Long programId;

  @Column(nullable = false)
  private String name;

  @Column
  private String logoURL;

  @OneToOne
  @JoinColumn(name = "program_fact_sheet_id")
  @JsonManagedReference
  private ProgramFactSheet programFactSheet;

  @OneToMany(mappedBy = "program")
  @JsonManagedReference
  private List<FundCodeEntity> fundCodes;

  @OneToOne
  @JoinColumn(name = "program_log_frame_id")
  @JsonManagedReference
  private ProgramLogFrame programLogFrame;

  @Column(name = "checked_by")
  private String checkedBy;

  @Column(name = "approved_by")
  private String approvedBy;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
  @Column(name = "checked_at")
  private LocalDateTime checkedAt;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
  @Column(name = "approved_at")
  private LocalDateTime approvedAt;

  @OneToMany(mappedBy = "program")
  @JsonManagedReference
  private List<Team> teams;

  @Column(name = "review_comment")
  private String reviewComment;

  @Column(name = "approve_comment")
  private String approveComment;

  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "status")
  private Status status;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "value_chain_id")
  @JsonBackReference
  private ValueChain valueChain;


}
