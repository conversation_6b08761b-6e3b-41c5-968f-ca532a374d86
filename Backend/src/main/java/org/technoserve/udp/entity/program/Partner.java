package org.technoserve.udp.entity.program;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.technoserve.udp.entity.common.AbstractEntity;
import org.technoserve.udp.entity.common.Status;


@Entity
@Table(name = "partner")
@Getter
@Setter
@NoArgsConstructor
public class Partner extends AbstractEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name="partner_id")
  private Long partnerId;

  @Column(name="name")
  private String name;

  @Column
  private String logoURL;

  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "status")
  @JsonIgnore
  private Status status;

}
