package org.technoserve.udp.entity.auth;

import jakarta.persistence.*;
import lombok.*;
import org.technoserve.udp.entity.common.AbstractEntity;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "endpoints", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"pattern", "httpMethod"})
})
public class EndPoint extends AbstractEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(nullable = false)
  private String pattern;  // e.g., "/admin/dashboard"

  @Column(nullable = false)
  private String httpMethod; // e.g., GET, POST

}
