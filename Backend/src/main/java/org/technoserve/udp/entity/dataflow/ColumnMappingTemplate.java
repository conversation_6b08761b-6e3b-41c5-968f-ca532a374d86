package org.technoserve.udp.entity.dataflow;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@Entity
@Table(name = "column_mapping_template")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ColumnMappingTemplate {

  @Id
  @Column(name = "column_mapping_template_id")
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long columnMappingTemplateId;

  @Column(name = "program_id")
  private Long programId;

  @Column(name = "partner_id")
  private Long partnerId;

  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "file_type")
  private FileType fileType;

  @Column(name = "entity_type", nullable = false)
  private String entityType;

  @Column(name = "excel_column")
  private String excelColumn;

  @Column(name = "entity_field")
  private String entityField;

}
