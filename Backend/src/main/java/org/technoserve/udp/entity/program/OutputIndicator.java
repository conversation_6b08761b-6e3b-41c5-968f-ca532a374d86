package org.technoserve.udp.entity.program;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.technoserve.udp.entity.common.AbstractEntity;
import org.technoserve.udp.entity.common.Status;

import java.math.BigDecimal;
import java.util.List;

@Entity
@Table(name="output_indicator")
@Getter
@Setter
@NoArgsConstructor
public class OutputIndicator extends AbstractEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column( name = "output_indicator_id")
  private Long outputIndicatorId;

  @Column
  private String outputIndicatorName;

  @Column
  private String unitOfMeasurement;

  @Column
  private BigDecimal yyTarget;


  @OneToMany(mappedBy = "outputIndicator", cascade = CascadeType.ALL)
  @JsonManagedReference
  private List<KeyActivity> keyActivities;

  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "status")
  @JsonIgnore
  private Status status;

  @ManyToOne
  @JoinColumn(name = "objective_id")
  @JsonBackReference
  private Objective objective;


}
