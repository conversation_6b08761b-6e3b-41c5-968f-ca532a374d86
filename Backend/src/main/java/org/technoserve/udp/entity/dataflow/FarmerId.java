package org.technoserve.udp.entity.dataflow;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Composite primary key for Farmer entity
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class FarmerId implements Serializable {

    private String farmerId;
    private Long programId;
    private Long partnerId;
}
