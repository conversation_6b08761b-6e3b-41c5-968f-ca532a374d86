package org.technoserve.udp.entity.common;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Entity
@Table(name = "master_state")
@Getter
@Setter
@NoArgsConstructor
public class MasterState {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long masterStateId;

  @Column(name="state_name")
  private String stateName;


  @OneToMany(mappedBy = "masterState")
  @JsonManagedReference
  private List<MasterDistrict> districts;

  @ManyToOne
  @JsonBackReference
  @JoinColumn(name = "master_country_id")
  private MasterCountry masterCountry;

}
