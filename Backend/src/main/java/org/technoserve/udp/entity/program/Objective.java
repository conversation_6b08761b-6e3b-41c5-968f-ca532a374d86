package org.technoserve.udp.entity.program;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.technoserve.udp.entity.common.AbstractEntity;
import org.technoserve.udp.entity.common.Status;

import java.math.BigDecimal;
import java.util.List;

@Entity
@Table(name="objective")
@Getter
@Setter
@NoArgsConstructor
public class Objective extends AbstractEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column( name = "objective_id" )
  private Long objectiveId;

  @Column
  private String kpiName;

  @Column
  private String logoURL;

  @Column
  private String outcomeIndicator;

  @Column
  private String unitOfMeasurement;

  @Column
  private BigDecimal endOfProgramTarget;

  @OneToMany(mappedBy = "objective", cascade = CascadeType.ALL)
  @JsonManagedReference
  private List<OutputIndicator> outputIndicators;

  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "status")
  @JsonIgnore
  private Status status;

  @ManyToOne
  @JoinColumn(name = "dimension_id")
  @JsonBackReference
  private Dimension dimension;

}
