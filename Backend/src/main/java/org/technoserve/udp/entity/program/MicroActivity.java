package org.technoserve.udp.entity.program;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.technoserve.udp.entity.common.AbstractEntity;
import org.technoserve.udp.entity.common.Status;

@Entity
@Table(name="micro_activity")
@Getter
@Setter
@NoArgsConstructor
public class MicroActivity extends AbstractEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column( name = "micro_activity_id")
  private Long microActivityId;

  @Column
  private String microActivityName;

  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "micro_activity_type")
  private MicroActivityType microActivityType;


  @Column
  private String tools;

  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "micro_activity_frequency")
  private MicroActivityFrequency frequency;

  @Column
  private String responsibleParty;

  @Column
  private String accountableParty;

  @Column
  private String consultedParty;

  @Column
  private String informedParty;

  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "status")
  @JsonIgnore
  private Status status;

  @OneToOne
  @JoinColumn(name = "budget_id")
  @JsonManagedReference
  private Budget budget;

  @ManyToOne
  @JoinColumn(name = "sub_activity_id")
  @JsonBackReference
  private SubActivity subActivity;

}
