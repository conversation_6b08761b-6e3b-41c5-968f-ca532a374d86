package org.technoserve.udp.config;
import org.springframework.data.domain.AuditorAware;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.stereotype.Component;
import org.technoserve.udp.entity.auth.UserEntity;
import org.technoserve.udp.repository.UserRepository;

import java.util.Optional;

@Component
public class OidcAuditorAwareImpl implements AuditorAware<String> {

    private final UserRepository userRepository;

    public OidcAuditorAwareImpl(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    @Override
    public Optional<String> getCurrentAuditor() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication == null || !authentication.isAuthenticated()) {
            return Optional.empty();
        }

        // Check if the user is authenticated via OIDC
        if (authentication.getPrincipal() instanceof OidcUser oidcUser) {
            String email = oidcUser.getEmail(); // Extract email from OIDC token
           Optional<UserEntity> user = userRepository.findById(email);
            // Fetch the UserEntity from DB (or create one if needed)
            return user.map(UserEntity::getEmail);
        }

        return Optional.empty();
    }
}
