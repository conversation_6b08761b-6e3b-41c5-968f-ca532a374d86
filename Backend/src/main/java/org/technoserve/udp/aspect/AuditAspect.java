package org.technoserve.udp.aspect;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.technoserve.udp.entity.audit.Auditable;
import org.technoserve.udp.service.AuditService;

@Aspect
@Component
@RequiredArgsConstructor
public class AuditAspect {

  private final AuditService auditService;

  @PersistenceContext
  private EntityManager entityManager;

  private boolean isAuditable(Object entity) {
    return entity.getClass().isAnnotationPresent(Auditable.class);
  }

  @AfterReturning(value = "execution(* jakarta.persistence.EntityManager.persist(..)) && args(entity)", argNames = "entity")
  public void afterInsert(Object entity) {
    if (isAuditable(entity)) {
      auditService.logChange(entity, "INSERT");
    }
  }

  @AfterReturning(value = "execution(* jakarta.persistence.EntityManager.merge(..)) && args(entity)", argNames = "entity")
  public void afterUpdate(Object entity) {
    if (isAuditable(entity)) {
      auditService.logChange(entity, "UPDATE");
    }
  }

  @AfterReturning(value = "execution(* jakarta.persistence.EntityManager.remove(..)) && args(entity)", argNames = "entity")
  public void afterDelete(Object entity) {
    if (isAuditable(entity)) {
      auditService.logChange(entity, "DELETE");
    }
  }
}
