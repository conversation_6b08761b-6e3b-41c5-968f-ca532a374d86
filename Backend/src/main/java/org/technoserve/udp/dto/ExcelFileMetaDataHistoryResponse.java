package org.technoserve.udp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.technoserve.udp.entity.dataflow.FileType;
import org.technoserve.udp.entity.dataflow.UploadStatus;

import java.time.LocalDateTime;

/**
 * DTO for Excel file metadata history response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExcelFileMetaDataHistoryResponse {
    private Long historyId;
    private Long excelFileMetaDataId;
    private String fileName;
    private FileType fileType;
    private String entityType;
    private String path;
    private String validationResultPath;
    private UploadStatus uploadStatus;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime uploadedOn;

    private String uploadedBy;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime processedOn;

    private String processedBy;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime archivedOn;

    private String archivedBy;
}
