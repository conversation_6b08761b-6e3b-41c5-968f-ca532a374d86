package org.technoserve.udp.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataflowColumnMappingResponse {
  private ExcelFileMetaDataResponse excelFileMetaData;
  private List<FieldMappingDto> mapping;
  private List<String> excelHeaders;
  private Set<String> requiredFields;
}