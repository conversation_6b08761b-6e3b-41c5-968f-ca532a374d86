package org.technoserve.udp.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for combined validation and processing response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ValidateAndProcessResponse {
    private boolean validationPassed;
    private int totalRows;
    private int validRows;
    private int invalidRows;
    private int processedRows;
    private String validatedFilePath;
    private String message;
}
